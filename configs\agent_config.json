{"data_sources": {"inventory_file": "data/raw/inventory.csv", "quality_file": "data/raw/quality.csv", "environment_file": "data/raw/environment.csv", "market_file": "data/raw/market.csv", "weather_api": {"url": "https://api.weather.com", "api_key": "your_weather_api_key"}, "market_api": {"url": "https://api.market.com", "api_key": "your_market_api_key"}}, "output_paths": {"reports": "data/results/reports", "predictions": "data/results/predictions", "alerts": "data/results/alerts", "recommendations": "data/results/recommendations"}, "analysis_config": {"alert_thresholds": {"temperature_max": 25.0, "temperature_min": 5.0, "humidity_max": 65.0, "humidity_min": 45.0, "inventory_min_days": 30, "quality_score_min": 80.0, "pest_level_max": 2}, "prediction_days": 30, "analysis_frequency": "daily", "data_retention_days": 365}, "grain_types": {"rice": {"name": "稻谷", "optimal_temp": "15-20", "optimal_humidity": "50-60", "storage_period": 365}, "wheat": {"name": "小麦", "optimal_temp": "10-15", "optimal_humidity": "45-55", "storage_period": 730}, "corn": {"name": "玉米", "optimal_temp": "10-15", "optimal_humidity": "50-60", "storage_period": 365}, "soybean": {"name": "大豆", "optimal_temp": "10-15", "optimal_humidity": "45-55", "storage_period": 365}}, "storage_locations": {"warehouse_001": {"name": "1号仓库", "capacity": 10000, "type": "平房仓", "location": "北区"}, "warehouse_002": {"name": "2号仓库", "capacity": 15000, "type": "立筒仓", "location": "南区"}, "warehouse_003": {"name": "3号仓库", "capacity": 8000, "type": "平房仓", "location": "东区"}}, "notification_config": {"email": {"enabled": true, "smtp_server": "smtp.company.com", "smtp_port": 587, "username": "<EMAIL>", "recipients": ["<EMAIL>", "<EMAIL>"]}, "sms": {"enabled": false, "api_url": "https://sms.api.com", "api_key": "your_sms_api_key"}, "webhook": {"enabled": true, "url": "https://your-system.com/webhook/grain-alerts"}}, "machine_learning": {"models": {"inventory_prediction": {"algorithm": "ARIMA", "parameters": {"order": [1, 1, 1], "seasonal_order": [1, 1, 1, 12]}}, "quality_prediction": {"algorithm": "RandomForest", "parameters": {"n_estimators": 100, "max_depth": 10}}, "anomaly_detection": {"algorithm": "IsolationForest", "parameters": {"contamination": 0.1, "random_state": 42}}}, "training_schedule": "weekly", "validation_split": 0.2, "feature_importance_threshold": 0.05}, "reporting": {"daily_report": {"enabled": true, "time": "08:00", "recipients": ["<EMAIL>"]}, "weekly_report": {"enabled": true, "day": "monday", "time": "09:00", "recipients": ["<EMAIL>", "<EMAIL>"]}, "monthly_report": {"enabled": true, "day": 1, "time": "10:00", "recipients": ["<EMAIL>", "<EMAIL>", "<EMAIL>"]}, "alert_report": {"enabled": true, "immediate": true, "recipients": ["<EMAIL>", "<EMAIL>"]}}, "system_config": {"log_level": "INFO", "max_log_size": "100MB", "backup_retention": 30, "database_backup_frequency": "daily", "performance_monitoring": true, "debug_mode": false}}