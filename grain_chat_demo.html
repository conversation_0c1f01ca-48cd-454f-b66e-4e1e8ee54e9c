<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粮库智能对话系统演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 800px;
            height: 600px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.bot {
            justify-content: flex-start;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
        }
        
        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }
        
        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }
        
        .chart-container {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .actions-container {
            margin-top: 10px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            padding: 6px 12px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
        }
        
        .quick-questions {
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }
        
        .quick-questions h4 {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .question-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .question-tag {
            padding: 6px 12px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .question-tag:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .input-field {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }
        
        .input-field:focus {
            border-color: #667eea;
        }
        
        .send-btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .send-btn:hover {
            background: #5a6fd8;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
        }
        
        .loading-dots {
            display: inline-block;
        }
        
        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        
        .error-message {
            color: #f44336;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .system-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background: rgba(255,255,255,0.2);
            border-radius: 15px;
            font-size: 12px;
            color: white;
        }
        
        .status-online {
            background: rgba(76, 175, 80, 0.8);
        }
        
        .status-offline {
            background: rgba(244, 67, 54, 0.8);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="system-status status-online" id="systemStatus">● 系统在线</div>
            <h1>🌾 粮库智能助手</h1>
            <p>您好！我是您的粮库数据分析助手，可以帮您查询温度、库存、质量等信息</p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-content">
                    <div>欢迎使用粮库智能对话系统！</div>
                    <div>您可以询问：</div>
                    <div>• 今天各仓库温度怎么样？</div>
                    <div>• 库存还够几天？</div>
                    <div>• 有哪些异常需要关注？</div>
                    <div class="message-time">系统消息</div>
                </div>
            </div>
        </div>
        
        <div class="quick-questions">
            <h4>💡 快捷问题</h4>
            <div class="question-tags" id="quickQuestions">
                <div class="question-tag" onclick="askQuestion('今天各仓库温度怎么样？')">今天温度情况</div>
                <div class="question-tag" onclick="askQuestion('库存还够几天？')">库存查询</div>
                <div class="question-tag" onclick="askQuestion('有哪些异常需要关注？')">异常检查</div>
                <div class="question-tag" onclick="askQuestion('生成今日温度报告')">生成报告</div>
            </div>
        </div>
        
        <div class="chat-input">
            <input 
                type="text" 
                class="input-field" 
                id="messageInput" 
                placeholder="请输入您的问题..."
                onkeypress="handleKeyPress(event)"
            >
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE_URL = 'http://localhost:8000/api';
        const USER_ID = 'demo_user_' + Date.now();
        
        // DOM元素
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const systemStatus = document.getElementById('systemStatus');
        
        // 状态管理
        let isLoading = false;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            loadQuickQuestions();
            messageInput.focus();
        });
        
        // 检查系统状态
        async function checkSystemStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    systemStatus.textContent = '● 系统在线';
                    systemStatus.className = 'system-status status-online';
                } else {
                    throw new Error('系统异常');
                }
            } catch (error) {
                systemStatus.textContent = '● 系统离线';
                systemStatus.className = 'system-status status-offline';
                console.error('系统状态检查失败:', error);
            }
        }
        
        // 加载快捷问题
        async function loadQuickQuestions() {
            try {
                const response = await fetch(`${API_BASE_URL}/quick-questions`);
                const data = await response.json();
                
                if (data.success) {
                    const container = document.getElementById('quickQuestions');
                    container.innerHTML = '';
                    
                    data.questions.forEach(question => {
                        const tag = document.createElement('div');
                        tag.className = 'question-tag';
                        tag.textContent = question;
                        tag.onclick = () => askQuestion(question);
                        container.appendChild(tag);
                    });
                }
            } catch (error) {
                console.error('加载快捷问题失败:', error);
            }
        }
        
        // 处理键盘事件
        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }
        
        // 快捷问题点击
        function askQuestion(question) {
            messageInput.value = question;
            sendMessage();
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isLoading) return;
            
            // 添加用户消息到界面
            addMessage('user', message);
            
            // 清空输入框
            messageInput.value = '';
            
            // 设置加载状态
            setLoading(true);
            
            // 添加加载消息
            const loadingId = addMessage('bot', '正在分析您的问题<span class="loading-dots"></span>');
            
            try {
                // 发送API请求
                const response = await fetch(`${API_BASE_URL}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: USER_ID,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                // 移除加载消息
                removeMessage(loadingId);
                
                if (data.success) {
                    // 添加回复消息
                    addBotResponse(data);
                } else {
                    addMessage('bot', `抱歉，处理您的问题时出现错误：${data.error || '未知错误'}`);
                }
                
            } catch (error) {
                // 移除加载消息
                removeMessage(loadingId);
                addMessage('bot', '抱歉，网络连接出现问题，请稍后重试。');
                console.error('发送消息失败:', error);
            } finally {
                setLoading(false);
            }
        }
        
        // 添加消息到界面
        function addMessage(type, content, data = null) {
            const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.id = messageId;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            const textDiv = document.createElement('div');
            textDiv.innerHTML = content;
            contentDiv.appendChild(textDiv);
            
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();
            contentDiv.appendChild(timeDiv);
            
            messageDiv.appendChild(contentDiv);
            chatMessages.appendChild(messageDiv);
            
            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            return messageId;
        }
        
        // 添加机器人回复
        function addBotResponse(data) {
            const messageId = addMessage('bot', data.text);
            const messageDiv = document.getElementById(messageId);
            const contentDiv = messageDiv.querySelector('.message-content');
            
            // 添加图表
            if (data.charts && data.charts.length > 0) {
                data.charts.forEach(chart => {
                    const chartDiv = document.createElement('div');
                    chartDiv.className = 'chart-container';
                    chartDiv.innerHTML = `📊 ${chart.title}<br><small>图表数据: ${JSON.stringify(chart.data).substring(0, 100)}...</small>`;
                    contentDiv.insertBefore(chartDiv, contentDiv.lastChild);
                });
            }
            
            // 添加操作按钮
            if (data.actions && data.actions.length > 0) {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'actions-container';
                
                data.actions.forEach(action => {
                    const btn = document.createElement('button');
                    btn.className = 'action-btn';
                    btn.textContent = action.label;
                    btn.onclick = () => handleAction(action);
                    actionsDiv.appendChild(btn);
                });
                
                contentDiv.insertBefore(actionsDiv, contentDiv.lastChild);
            }
        }
        
        // 处理操作按钮点击
        function handleAction(action) {
            switch (action.id) {
                case 'export_temp_report':
                    generateReport('temperature');
                    break;
                case 'export_inventory_report':
                    generateReport('inventory');
                    break;
                case 'set_temp_alert':
                    askQuestion('设置温度预警阈值');
                    break;
                case 'inventory_forecast':
                    askQuestion('预测未来库存变化');
                    break;
                default:
                    console.log('未知操作:', action);
            }
        }
        
        // 生成报告
        async function generateReport(reportType) {
            try {
                addMessage('bot', '正在生成报告，请稍候...');
                
                const response = await fetch(`${API_BASE_URL}/generate-report`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: USER_ID,
                        report_type: reportType
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const downloadLink = `${API_BASE_URL.replace('/api', '')}${data.download_url}`;
                    addMessage('bot', `报告生成成功！<br><a href="${downloadLink}" target="_blank">点击下载报告</a>`);
                } else {
                    addMessage('bot', '报告生成失败，请稍后重试。');
                }
                
            } catch (error) {
                addMessage('bot', '报告生成失败，请检查网络连接。');
                console.error('生成报告失败:', error);
            }
        }
        
        // 移除消息
        function removeMessage(messageId) {
            const messageDiv = document.getElementById(messageId);
            if (messageDiv) {
                messageDiv.remove();
            }
        }
        
        // 设置加载状态
        function setLoading(loading) {
            isLoading = loading;
            sendBtn.disabled = loading;
            messageInput.disabled = loading;
            
            if (loading) {
                sendBtn.textContent = '发送中...';
            } else {
                sendBtn.textContent = '发送';
                messageInput.focus();
            }
        }
        
        // 定期检查系统状态
        setInterval(checkSystemStatus, 30000); // 每30秒检查一次
    </script>
</body>
</html>
