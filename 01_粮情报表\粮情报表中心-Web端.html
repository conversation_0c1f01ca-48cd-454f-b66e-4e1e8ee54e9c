<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>粮情报表中心 - Web管理端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 28px;
            font-weight: bold;
        }
        
        .header-info {
            text-align: right;
        }
        
        .header-info .user {
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .header-info .time {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: white;
            font-size: 16px;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }
        
        .nav-tab:last-child {
            border-right: none;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .nav-tab:hover:not(.active) {
            background: #f8f9fa;
        }
        
        .report-content {
            display: none;
        }
        
        .report-content.active {
            display: block;
        }
        
        .report-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .report-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .report-meta {
            color: #666;
            font-size: 14px;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .warehouse-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .warehouse-table th,
        .warehouse-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .warehouse-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .warehouse-table tr:hover {
            background: #f5f5f5;
        }
        
        .status-normal {
            color: #4caf50;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ff9800;
            font-weight: bold;
        }
        
        .status-danger {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-icon {
            font-size: 16px;
            margin-right: 5px;
        }
        
        .alert-list {
            list-style: none;
        }
        
        .alert-item {
            padding: 10px;
            margin-bottom: 8px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-urgent {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .alert-warning {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        
        .alert-normal {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .schedule-timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .schedule-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ddd;
        }
        
        .schedule-item {
            position: relative;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .schedule-item::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 20px;
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
        }
        
        .schedule-time {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .schedule-tasks {
            list-style: none;
        }
        
        .schedule-tasks li {
            margin-bottom: 5px;
            padding-left: 15px;
            position: relative;
        }
        
        .schedule-tasks li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #667eea;
        }
        
        .chart-placeholder {
            height: 200px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            border: 2px dashed #dee2e6;
            text-align: center;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .nav-tab {
                border-right: none;
                border-bottom: 1px solid #eee;
            }
            
            .cards-grid {
                grid-template-columns: 1fr;
            }
            
            .overview-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>🌾 粮情报表中心</h1>
            <div class="header-info">
                <div class="user">👤 保管员：张三</div>
                <div class="time" id="currentTime">2024年1月15日 14:30</div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showReport('daily')">📅 日报</button>
            <button class="nav-tab" onclick="showReport('weekly')">📊 周报</button>
            <button class="nav-tab" onclick="showReport('monthly')">📈 月报</button>
            <button class="nav-tab" onclick="showReport('settings')">⚙️ 设置</button>
        </div>
        
        <!-- 日报内容 -->
        <div id="daily" class="report-content active">
            <div class="report-header">
                <div class="report-title">
                    📊 保管员张三粮情日报
                </div>
                <div class="report-meta">
                    📅 日期: 2024年1月15日 | 🏠 管理仓房: 1号仓、3号仓、5号仓 (共3个仓房)
                </div>
            </div>
            
            <div class="cards-grid">
                <!-- 整体概况 -->
                <div class="card">
                    <div class="card-title">📋 整体概况</div>
                    <div class="overview-stats">
                        <div class="stat-item">
                            <div class="stat-value">3</div>
                            <div class="stat-label">管理仓房总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">5300</div>
                            <div class="stat-label">粮食总存量(吨)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value status-warning">需关注</div>
                            <div class="stat-label">整体状况</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">3号仓</div>
                            <div class="stat-label">今日重点关注</div>
                        </div>
                    </div>
                </div>
                
                <!-- 环境监测汇总 -->
                <div class="card">
                    <div class="card-title">🌡️ 环境监测汇总</div>
                    <div class="overview-stats">
                        <div class="stat-item">
                            <div class="stat-value">18.5°C</div>
                            <div class="stat-label">平均温度 (↑0.3°C)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">17.8~19.1°C</div>
                            <div class="stat-label">温度范围</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">58.4%</div>
                            <div class="stat-label">平均湿度 (↑1.2%)</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value status-warning">1个</div>
                            <div class="stat-label">异常仓房</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 各仓粮食状况 -->
            <div class="card">
                <div class="card-title">🌾 各仓粮食状况</div>
                <table class="warehouse-table">
                    <thead>
                        <tr>
                            <th>仓房</th>
                            <th>粮食品种</th>
                            <th>存量(吨)</th>
                            <th>质量等级</th>
                            <th>温度</th>
                            <th>湿度</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1号仓</td>
                            <td>稻谷</td>
                            <td>2000</td>
                            <td>A级</td>
                            <td>18.2°C</td>
                            <td>55%</td>
                            <td><span class="status-normal"><span class="status-icon">✅</span>正常</span></td>
                        </tr>
                        <tr>
                            <td>3号仓</td>
                            <td>小麦</td>
                            <td>1500</td>
                            <td>A级</td>
                            <td>19.1°C</td>
                            <td>62%</td>
                            <td><span class="status-warning"><span class="status-icon">⚠️</span>需关注</span></td>
                        </tr>
                        <tr>
                            <td>5号仓</td>
                            <td>玉米</td>
                            <td>1800</td>
                            <td>B级</td>
                            <td>17.8°C</td>
                            <td>58%</td>
                            <td><span class="status-normal"><span class="status-icon">✅</span>正常</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="cards-grid">
                <!-- 今日预警 -->
                <div class="card">
                    <div class="card-title">⚠️ 今日预警与重点关注</div>
                    <ul class="alert-list">
                        <li class="alert-item alert-warning">
                            <span class="status-icon">🟡</span>
                            <div>
                                <strong>注意:</strong> 3号仓温度偏高(19.1°C)，需立即通风
                            </div>
                        </li>
                        <li class="alert-item alert-warning">
                            <span class="status-icon">🟡</span>
                            <div>
                                <strong>注意:</strong> 5号仓湿度上升趋势，密切监控
                            </div>
                        </li>
                        <li class="alert-item alert-normal">
                            <span class="status-icon">🟢</span>
                            <div>
                                <strong>正常:</strong> 1号仓各项指标稳定
                            </div>
                        </li>
                    </ul>
                </div>
                
                <!-- 今日作业安排 -->
                <div class="card">
                    <div class="card-title">💡 今日作业安排</div>
                    <div class="schedule-timeline">
                        <div class="schedule-item">
                            <div class="schedule-time">上午 (8:00-12:00)</div>
                            <ul class="schedule-tasks">
                                <li>3号仓: 开启通风设备，降温处理</li>
                                <li>1号仓: 常规巡检，重点检查东南角</li>
                                <li>5号仓: 湿度监测，记录变化</li>
                            </ul>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-time">下午 (14:00-18:00)</div>
                            <ul class="schedule-tasks">
                                <li>3号仓: 检查通风效果，记录温度变化</li>
                                <li>全部仓房: 安全检查，设备维护</li>
                                <li>数据记录: 更新各仓房监测数据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 趋势分析 -->
            <div class="card">
                <div class="card-title">📈 趋势分析</div>
                <div class="chart-placeholder">
                    📊 温湿度变化趋势图<br>
                    (可集成 ECharts 显示实时数据)
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出日报</button>
                <button class="btn btn-secondary">📧 发送邮件</button>
                <button class="btn btn-success">✅ 确认已读</button>
            </div>
        </div>
        
        <!-- 周报内容 -->
        <div id="weekly" class="report-content">
            <div class="report-header">
                <div class="report-title">
                    📊 保管员张三粮情周报
                </div>
                <div class="report-meta">
                    📅 周期: 2024年1月8日 至 2024年1月14日 | 🏠 管理仓房: 1号仓、3号仓、5号仓
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">🏆 各仓房表现排名</div>
                <table class="warehouse-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>仓房</th>
                            <th>平均温度</th>
                            <th>平均湿度</th>
                            <th>异常次数</th>
                            <th>评价</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>🥇</td>
                            <td>1号仓</td>
                            <td>18.2°C</td>
                            <td>55%</td>
                            <td>0次</td>
                            <td><span class="status-normal">优秀</span></td>
                        </tr>
                        <tr>
                            <td>🥈</td>
                            <td>5号仓</td>
                            <td>17.8°C</td>
                            <td>58%</td>
                            <td>0次</td>
                            <td><span class="status-normal">良好</span></td>
                        </tr>
                        <tr>
                            <td>⚠️</td>
                            <td>3号仓</td>
                            <td>19.1°C</td>
                            <td>62%</td>
                            <td>2次</td>
                            <td><span class="status-warning">需关注</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出周报</button>
                <button class="btn btn-secondary">📧 发送邮件</button>
            </div>
        </div>
        
        <!-- 月报内容 -->
        <div id="monthly" class="report-content">
            <div class="report-header">
                <div class="report-title">
                    📊 保管员张三粮情月报
                </div>
                <div class="report-meta">
                    📅 月份: 2024年1月 | 🏠 管理仓房: 1号仓、3号仓、5号仓
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">📈 月度整体概况</div>
                <div class="overview-stats">
                    <div class="stat-item">
                        <div class="stat-value">3</div>
                        <div class="stat-label">管理仓房</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">5300</div>
                        <div class="stat-label">总存量(吨)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">18.5°C</div>
                        <div class="stat-label">月均温度</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">良好</div>
                        <div class="stat-label">月度评级</div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出月报</button>
                <button class="btn btn-secondary">📧 发送邮件</button>
            </div>
        </div>
        
        <!-- 设置内容 -->
        <div id="settings" class="report-content">
            <div class="report-header">
                <div class="report-title">
                    ⚙️ 报表设置
                </div>
                <div class="report-meta">
                    个性化配置您的报表偏好
                </div>
            </div>
            
            <div class="card">
                <div class="card-title">📧 推送设置</div>
                <p>配置报表自动推送的时间和方式...</p>
            </div>
            
            <div class="card">
                <div class="card-title">⚠️ 预警阈值</div>
                <p>设置温度、湿度等预警阈值...</p>
            </div>
        </div>
    </div>
    
    <script>
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 切换报表
        function showReport(reportType) {
            // 隐藏所有报表内容
            const contents = document.querySelectorAll('.report-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的活动状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的报表内容
            document.getElementById(reportType).classList.add('active');
            
            // 激活对应的标签
            event.target.classList.add('active');
        }
        
        // 初始化
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新一次时间
    </script>
</body>
</html>
