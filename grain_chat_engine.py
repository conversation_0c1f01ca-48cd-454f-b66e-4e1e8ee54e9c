#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
粮库智能对话引擎
Grain Storage Intelligent Chat Engine

核心功能：
1. 自然语言理解
2. 意图识别和实体提取
3. 查询生成和数据分析
4. 智能回复生成
"""

import re
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass

@dataclass
class ChatMessage:
    """聊天消息数据结构"""
    user_id: str
    message: str
    timestamp: datetime
    intent: Optional[str] = None
    entities: Optional[Dict] = None
    response: Optional[str] = None

@dataclass
class AnalysisResult:
    """分析结果数据结构"""
    summary: str
    data: Dict[str, Any]
    charts: List[Dict]
    recommendations: List[str]
    alerts: List[str]

class GrainChatEngine:
    """粮库智能对话引擎"""
    
    def __init__(self, config_path: str = "configs/chat_config.json"):
        """初始化对话引擎"""
        self.config = self._load_config(config_path)
        self.intent_classifier = IntentClassifier()
        self.entity_extractor = EntityExtractor()
        self.query_generator = QueryGenerator()
        self.data_analyzer = DataAnalyzer()
        self.response_generator = ResponseGenerator()
        self.context_manager = ContextManager()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        self.logger.info("粮库智能对话引擎初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        default_config = {
            "intent_threshold": 0.8,
            "max_context_turns": 5,
            "supported_intents": [
                "temperature_query",
                "inventory_query", 
                "quality_query",
                "alert_query",
                "trend_analysis",
                "report_generation"
            ],
            "quick_questions": [
                "今天各仓库温度怎么样？",
                "库存还够几天？",
                "有哪些异常需要关注？",
                "生成今日温度报告"
            ]
        }
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        except FileNotFoundError:
            self.logger.warning(f"配置文件 {config_path} 不存在，使用默认配置")
        
        return default_config
    
    def process_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """处理用户消息的主入口"""
        self.logger.info(f"处理用户 {user_id} 的消息: {message}")
        
        try:
            # 1. 创建消息对象
            chat_message = ChatMessage(
                user_id=user_id,
                message=message,
                timestamp=datetime.now()
            )
            
            # 2. 获取对话上下文
            context = self.context_manager.get_context(user_id)
            
            # 3. 意图识别和实体提取
            intent_result = self.intent_classifier.classify(message, context)
            entities = self.entity_extractor.extract(message, context)
            
            chat_message.intent = intent_result["intent"]
            chat_message.entities = entities
            
            # 4. 生成数据查询
            query_info = self.query_generator.generate(intent_result["intent"], entities)
            
            # 5. 执行数据分析
            analysis_result = self.data_analyzer.analyze(query_info)
            
            # 6. 生成智能回复
            response = self.response_generator.generate(
                intent_result["intent"], 
                entities, 
                analysis_result
            )
            
            chat_message.response = response["text"]
            
            # 7. 更新对话上下文
            self.context_manager.update_context(user_id, chat_message)
            
            # 8. 返回结果
            return {
                "success": True,
                "text": response["text"],
                "charts": response.get("charts", []),
                "actions": response.get("actions", []),
                "intent": intent_result["intent"],
                "confidence": intent_result["confidence"]
            }
            
        except Exception as e:
            self.logger.error(f"处理消息时发生错误: {str(e)}")
            return {
                "success": False,
                "text": "抱歉，我暂时无法理解您的问题，请尝试换个方式提问。",
                "error": str(e)
            }

class IntentClassifier:
    """意图分类器"""
    
    def __init__(self):
        """初始化意图分类器"""
        # 意图关键词映射
        self.intent_keywords = {
            "temperature_query": {
                "keywords": ["温度", "粮温", "热度", "度数", "温控"],
                "patterns": [r"温度.*怎么样", r".*温度.*情况", r"粮温.*监控"]
            },
            "inventory_query": {
                "keywords": ["库存", "存量", "剩余", "还有", "数量", "吨数"],
                "patterns": [r"还有.*多少", r"库存.*情况", r"剩余.*数量"]
            },
            "quality_query": {
                "keywords": ["质量", "品质", "等级", "检测", "合格"],
                "patterns": [r"质量.*怎么样", r"品质.*如何", r".*等级"]
            },
            "alert_query": {
                "keywords": ["预警", "异常", "问题", "故障", "报警", "注意"],
                "patterns": [r"有.*异常", r"需要.*关注", r".*预警"]
            },
            "trend_analysis": {
                "keywords": ["趋势", "变化", "走势", "分析", "对比"],
                "patterns": [r".*趋势", r"变化.*情况", r".*分析"]
            },
            "report_generation": {
                "keywords": ["报告", "报表", "统计", "汇总", "生成"],
                "patterns": [r"生成.*报告", r".*报表", r"统计.*数据"]
            }
        }
    
    def classify(self, message: str, context: List[ChatMessage] = None) -> Dict[str, Any]:
        """分类用户意图"""
        message_lower = message.lower()
        intent_scores = {}
        
        # 基于关键词和模式匹配计算意图得分
        for intent, config in self.intent_keywords.items():
            score = 0
            
            # 关键词匹配
            for keyword in config["keywords"]:
                if keyword in message_lower:
                    score += 1
            
            # 模式匹配
            for pattern in config["patterns"]:
                if re.search(pattern, message):
                    score += 2
            
            intent_scores[intent] = score
        
        # 找到最高得分的意图
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            confidence = min(intent_scores[best_intent] / 3.0, 1.0)  # 归一化到0-1
            
            if confidence > 0.3:  # 置信度阈值
                return {
                    "intent": best_intent,
                    "confidence": confidence,
                    "all_scores": intent_scores
                }
        
        # 默认意图
        return {
            "intent": "general_query",
            "confidence": 0.5,
            "all_scores": intent_scores
        }

class EntityExtractor:
    """实体提取器"""
    
    def __init__(self):
        """初始化实体提取器"""
        # 实体模式定义
        self.entity_patterns = {
            "warehouse": [
                r'(\d+号?仓库?)',
                r'(仓库\d+)',
                r'([A-Z]+\d+仓)',
                r'(\d+仓)'
            ],
            "grain_type": [
                r'(稻谷|大米|水稻)',
                r'(小麦|麦子)',
                r'(玉米|苞米)',
                r'(大豆|黄豆)',
                r'(高粱)',
                r'(花生)'
            ],
            "time_range": [
                r'(今天|今日)',
                r'(昨天|昨日)',
                r'(本周|这周)',
                r'(本月|这个月)',
                r'(最近\d+天)',
                r'(\d+月\d+日)'
            ],
            "metric": [
                r'(温度|粮温)',
                r'(湿度)',
                r'(库存|存量)',
                r'(质量|品质)'
            ]
        }
    
    def extract(self, message: str, context: List[ChatMessage] = None) -> Dict[str, List[str]]:
        """提取实体"""
        entities = {}
        
        for entity_type, patterns in self.entity_patterns.items():
            matches = []
            for pattern in patterns:
                found = re.findall(pattern, message)
                matches.extend(found)
            
            if matches:
                entities[entity_type] = list(set(matches))  # 去重
        
        # 如果没有提取到仓库信息，从上下文中获取
        if "warehouse" not in entities and context:
            for prev_message in reversed(context[-3:]):  # 查看最近3条消息
                if prev_message.entities and "warehouse" in prev_message.entities:
                    entities["warehouse"] = prev_message.entities["warehouse"]
                    break
        
        # 时间范围标准化
        if "time_range" in entities:
            entities["time_range"] = self._normalize_time_range(entities["time_range"])
        
        return entities
    
    def _normalize_time_range(self, time_expressions: List[str]) -> List[str]:
        """标准化时间表达式"""
        normalized = []
        for expr in time_expressions:
            if expr in ["今天", "今日"]:
                normalized.append("today")
            elif expr in ["昨天", "昨日"]:
                normalized.append("yesterday")
            elif expr in ["本周", "这周"]:
                normalized.append("this_week")
            elif expr in ["本月", "这个月"]:
                normalized.append("this_month")
            else:
                normalized.append(expr)
        return normalized

class QueryGenerator:
    """查询生成器"""
    
    def __init__(self):
        """初始化查询生成器"""
        self.query_templates = {
            "temperature_query": {
                "table": "sensor_data",
                "fields": ["warehouse_id", "temperature", "timestamp"],
                "conditions": ["timestamp >= '{start_time}'", "timestamp <= '{end_time}'"]
            },
            "inventory_query": {
                "table": "inventory",
                "fields": ["warehouse_id", "grain_type", "quantity", "date"],
                "conditions": ["date = '{date}'"]
            },
            "quality_query": {
                "table": "quality_tests",
                "fields": ["warehouse_id", "grain_type", "quality_score", "test_date"],
                "conditions": ["test_date >= '{start_time}'"]
            }
        }
    
    def generate(self, intent: str, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """生成查询信息"""
        if intent not in self.query_templates:
            return {"type": "unknown", "query": None}
        
        template = self.query_templates[intent]
        
        # 构建查询条件
        conditions = template["conditions"].copy()
        
        # 添加仓库条件
        if "warehouse" in entities:
            warehouse_list = "','".join(entities["warehouse"])
            conditions.append(f"warehouse_id IN ('{warehouse_list}')")
        
        # 添加粮食类型条件
        if "grain_type" in entities:
            grain_list = "','".join(entities["grain_type"])
            conditions.append(f"grain_type IN ('{grain_list}')")
        
        # 处理时间范围
        time_range = self._parse_time_range(entities.get("time_range", ["today"]))
        
        return {
            "type": intent,
            "table": template["table"],
            "fields": template["fields"],
            "conditions": conditions,
            "time_range": time_range,
            "entities": entities
        }
    
    def _parse_time_range(self, time_expressions: List[str]) -> Dict[str, str]:
        """解析时间范围"""
        now = datetime.now()
        
        if "today" in time_expressions:
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif "yesterday" in time_expressions:
            yesterday = now - timedelta(days=1)
            start_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        elif "this_week" in time_expressions:
            start_time = now - timedelta(days=now.weekday())
            start_time = start_time.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now
        else:
            # 默认今天
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now
        
        return {
            "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S")
        }

class DataAnalyzer:
    """数据分析器"""
    
    def __init__(self):
        """初始化数据分析器"""
        # 模拟数据源
        self.mock_data = self._generate_mock_data()
    
    def analyze(self, query_info: Dict[str, Any]) -> AnalysisResult:
        """执行数据分析"""
        query_type = query_info["type"]
        
        if query_type == "temperature_query":
            return self._analyze_temperature(query_info)
        elif query_type == "inventory_query":
            return self._analyze_inventory(query_info)
        elif query_type == "quality_query":
            return self._analyze_quality(query_info)
        else:
            return self._default_analysis(query_info)
    
    def _analyze_temperature(self, query_info: Dict[str, Any]) -> AnalysisResult:
        """分析温度数据"""
        # 模拟温度数据分析
        warehouses = query_info["entities"].get("warehouse", ["1号仓库"])
        
        temp_data = []
        alerts = []
        
        for warehouse in warehouses:
            avg_temp = 18.5 + hash(warehouse) % 10 * 0.1  # 模拟数据
            max_temp = avg_temp + 2.0
            min_temp = avg_temp - 1.5
            
            temp_data.append({
                "warehouse": warehouse,
                "avg_temp": round(avg_temp, 1),
                "max_temp": round(max_temp, 1),
                "min_temp": round(min_temp, 1)
            })
            
            # 检查预警
            if max_temp > 25.0:
                alerts.append(f"{warehouse}最高温度{max_temp}°C，超过安全阈值")
        
        # 生成图表配置
        charts = [{
            "type": "line",
            "title": "温度变化趋势",
            "data": [
                {"time": "08:00", "value": 18.2},
                {"time": "12:00", "value": 19.1},
                {"time": "16:00", "value": 18.8},
                {"time": "20:00", "value": 18.5}
            ]
        }]
        
        # 生成总结
        if len(warehouses) == 1:
            summary = f"{warehouses[0]}今日平均温度{temp_data[0]['avg_temp']}°C，温度范围{temp_data[0]['min_temp']}°C-{temp_data[0]['max_temp']}°C，整体稳定。"
        else:
            avg_all = sum(item['avg_temp'] for item in temp_data) / len(temp_data)
            summary = f"查询的{len(warehouses)}个仓库平均温度{avg_all:.1f}°C，温度控制良好。"
        
        recommendations = []
        if not alerts:
            recommendations.append("温度控制良好，继续保持当前管理水平")
        else:
            recommendations.append("建议加强高温仓库的通风降温措施")
        
        return AnalysisResult(
            summary=summary,
            data={"temperature_data": temp_data},
            charts=charts,
            recommendations=recommendations,
            alerts=alerts
        )
    
    def _analyze_inventory(self, query_info: Dict[str, Any]) -> AnalysisResult:
        """分析库存数据"""
        # 模拟库存分析
        grain_types = query_info["entities"].get("grain_type", ["稻谷", "小麦"])
        
        inventory_data = []
        alerts = []
        
        for grain in grain_types:
            quantity = 5000 + hash(grain) % 3000  # 模拟数据
            days_remaining = quantity / 50  # 假设每天消耗50吨
            
            inventory_data.append({
                "grain_type": grain,
                "quantity": quantity,
                "days_remaining": int(days_remaining)
            })
            
            if days_remaining < 30:
                alerts.append(f"{grain}库存仅够{int(days_remaining)}天，建议及时补货")
        
        total_quantity = sum(item['quantity'] for item in inventory_data)
        summary = f"当前库存总量{total_quantity}吨，包含{len(grain_types)}种粮食。"
        
        charts = [{
            "type": "bar",
            "title": "各类粮食库存分布",
            "data": [{"name": item["grain_type"], "value": item["quantity"]} for item in inventory_data]
        }]
        
        recommendations = ["建议定期检查库存变化，确保供应链稳定"]
        
        return AnalysisResult(
            summary=summary,
            data={"inventory_data": inventory_data},
            charts=charts,
            recommendations=recommendations,
            alerts=alerts
        )
    
    def _default_analysis(self, query_info: Dict[str, Any]) -> AnalysisResult:
        """默认分析"""
        return AnalysisResult(
            summary="我正在学习如何更好地回答这类问题，请尝试询问温度、库存或质量相关的问题。",
            data={},
            charts=[],
            recommendations=["您可以询问：今天温度怎么样？库存还有多少？"],
            alerts=[]
        )
    
    def _generate_mock_data(self) -> Dict:
        """生成模拟数据"""
        return {
            "warehouses": ["1号仓库", "2号仓库", "3号仓库"],
            "grain_types": ["稻谷", "小麦", "玉米", "大豆"]
        }

class ResponseGenerator:
    """回复生成器"""
    
    def generate(self, intent: str, entities: Dict, analysis_result: AnalysisResult) -> Dict[str, Any]:
        """生成智能回复"""
        response_text = analysis_result.summary
        
        # 添加预警信息
        if analysis_result.alerts:
            response_text += "\n\n⚠️ 预警信息：\n" + "\n".join(f"• {alert}" for alert in analysis_result.alerts)
        
        # 添加建议
        if analysis_result.recommendations:
            response_text += "\n\n💡 建议：\n" + "\n".join(f"• {rec}" for rec in analysis_result.recommendations)
        
        # 生成操作按钮
        actions = []
        if intent == "temperature_query":
            actions = [
                {"id": "export_temp_report", "label": "导出温度报告"},
                {"id": "set_temp_alert", "label": "设置温度预警"}
            ]
        elif intent == "inventory_query":
            actions = [
                {"id": "export_inventory_report", "label": "导出库存报告"},
                {"id": "inventory_forecast", "label": "库存预测"}
            ]
        
        return {
            "text": response_text,
            "charts": analysis_result.charts,
            "actions": actions,
            "data": analysis_result.data
        }

class ContextManager:
    """对话上下文管理器"""
    
    def __init__(self, max_context_turns: int = 5):
        """初始化上下文管理器"""
        self.contexts = {}  # user_id -> List[ChatMessage]
        self.max_turns = max_context_turns
    
    def get_context(self, user_id: str) -> List[ChatMessage]:
        """获取用户对话上下文"""
        return self.contexts.get(user_id, [])
    
    def update_context(self, user_id: str, message: ChatMessage):
        """更新用户对话上下文"""
        if user_id not in self.contexts:
            self.contexts[user_id] = []
        
        self.contexts[user_id].append(message)
        
        # 保持上下文长度
        if len(self.contexts[user_id]) > self.max_turns:
            self.contexts[user_id] = self.contexts[user_id][-self.max_turns:]

# 使用示例
if __name__ == "__main__":
    # 创建对话引擎
    chat_engine = GrainChatEngine()
    
    # 模拟对话
    test_messages = [
        "今天1号仓库的温度怎么样？",
        "库存还有多少稻谷？",
        "有什么异常需要关注吗？",
        "生成今日温度报告"
    ]
    
    user_id = "test_user_001"
    
    for message in test_messages:
        print(f"\n用户: {message}")
        response = chat_engine.process_message(user_id, message)
        print(f"系统: {response['text']}")
        
        if response.get('charts'):
            print(f"图表: {len(response['charts'])}个")
        
        if response.get('actions'):
            print(f"操作: {[action['label'] for action in response['actions']]}")
        
        print("-" * 50)
