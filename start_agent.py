#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
粮库数据分析智能体启动脚本
Grain Storage AI Agent Startup Script
"""

import sys
import os
import argparse
from datetime import datetime
from grain_ai_agent import GrainStorageAIAgent

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    粮库数据分析智能体                          ║
    ║                Grain Storage AI Agent                        ║
    ║                                                              ║
    ║  🌾 智能分析 | 📊 数据洞察 | ⚠️ 预警监控 | 📈 趋势预测        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)
    print(f"    启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("    " + "="*60)

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("   请运行: pip install pandas numpy matplotlib seaborn")
        return False
    
    # 检查数据目录
    required_dirs = ['data/raw', 'data/results', 'configs', 'logs']
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"📁 创建目录: {directory}")
            os.makedirs(directory, exist_ok=True)
    
    print("✅ 环境检查通过")
    return True

def check_data_files():
    """检查数据文件"""
    print("📋 检查数据文件...")
    
    data_files = [
        'data/raw/inventory.csv',
        'data/raw/environment.csv', 
        'data/raw/quality.csv'
    ]
    
    missing_files = []
    for file_path in data_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("⚠️  以下数据文件不存在:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("   智能体将使用示例数据运行")
    else:
        print("✅ 数据文件检查通过")
    
    return True

def run_daily_analysis():
    """运行日常分析"""
    print("\n🚀 启动粮库数据分析智能体...")
    
    try:
        # 创建智能体实例
        agent = GrainStorageAIAgent()
        
        # 执行日常分析
        print("📊 执行日常分析任务...")
        success = agent.run_daily_analysis()
        
        if success:
            print("✅ 日常分析任务执行成功!")
            print("\n📁 分析结果保存位置:")
            print("   - 分析报告: data/results/reports/")
            print("   - 预警信息: data/results/alerts/")
            print("   - 预测结果: data/results/predictions/")
            print("   - 建议方案: data/results/recommendations/")
        else:
            print("❌ 日常分析任务执行失败，请查看日志文件")
            
        return success
        
    except Exception as e:
        print(f"❌ 智能体运行出错: {str(e)}")
        print("   请查看日志文件获取详细错误信息")
        return False

def run_custom_analysis(analysis_type, parameters=None):
    """运行自定义分析"""
    print(f"\n🔬 执行自定义分析: {analysis_type}")
    
    try:
        agent = GrainStorageAIAgent()
        report_path = agent.generate_custom_report(analysis_type, parameters)
        
        if report_path:
            print(f"✅ 自定义分析完成，报告保存至: {report_path}")
        else:
            print("❌ 自定义分析失败")
            
        return report_path is not None
        
    except Exception as e:
        print(f"❌ 自定义分析出错: {str(e)}")
        return False

def interactive_mode():
    """交互模式"""
    print("\n🎯 进入交互模式")
    print("可用命令:")
    print("  1. daily    - 执行日常分析")
    print("  2. custom   - 自定义分析")
    print("  3. status   - 查看系统状态")
    print("  4. help     - 显示帮助")
    print("  5. exit     - 退出程序")
    
    while True:
        try:
            command = input("\n请输入命令 > ").strip().lower()
            
            if command == "daily" or command == "1":
                run_daily_analysis()
                
            elif command == "custom" or command == "2":
                print("可用的分析类型:")
                print("  - inventory_analysis  (库存分析)")
                print("  - quality_trend      (质量趋势)")
                print("  - environment_report (环境报告)")
                
                analysis_type = input("请输入分析类型 > ").strip()
                if analysis_type:
                    run_custom_analysis(analysis_type)
                    
            elif command == "status" or command == "3":
                print("📊 系统状态:")
                print(f"   - 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"   - Python版本: {sys.version.split()[0]}")
                print(f"   - 工作目录: {os.getcwd()}")
                
            elif command == "help" or command == "4":
                print("📖 帮助信息:")
                print("   本智能体用于粮库数据的自动化分析")
                print("   支持库存、质量、环境等多维度分析")
                print("   所有分析结果都会保存到data/results/目录")
                
            elif command == "exit" or command == "5":
                print("👋 感谢使用粮库数据分析智能体!")
                break
                
            else:
                print("❓ 未知命令，请输入 help 查看可用命令")
                
        except KeyboardInterrupt:
            print("\n👋 感谢使用粮库数据分析智能体!")
            break
        except Exception as e:
            print(f"❌ 命令执行出错: {str(e)}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='粮库数据分析智能体')
    parser.add_argument('--mode', choices=['daily', 'custom', 'interactive'], 
                       default='daily', help='运行模式')
    parser.add_argument('--analysis-type', help='自定义分析类型')
    parser.add_argument('--no-banner', action='store_true', help='不显示启动横幅')
    
    args = parser.parse_args()
    
    # 显示启动横幅
    if not args.no_banner:
        print_banner()
    
    # 环境检查
    if not check_environment():
        sys.exit(1)
    
    # 数据文件检查
    check_data_files()
    
    # 根据模式执行相应操作
    if args.mode == 'daily':
        success = run_daily_analysis()
        sys.exit(0 if success else 1)
        
    elif args.mode == 'custom':
        if not args.analysis_type:
            print("❌ 自定义分析模式需要指定 --analysis-type 参数")
            sys.exit(1)
        success = run_custom_analysis(args.analysis_type)
        sys.exit(0 if success else 1)
        
    elif args.mode == 'interactive':
        interactive_mode()
        
    else:
        print("❌ 未知的运行模式")
        sys.exit(1)

if __name__ == "__main__":
    main()
