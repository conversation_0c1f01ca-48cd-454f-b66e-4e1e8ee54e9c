#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
粮库智能对话API服务
Grain Storage Chat API Service

提供RESTful API接口，支持：
1. 聊天对话接口
2. 历史记录查询
3. 报告生成接口
4. 系统状态监控
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import json
import os
from datetime import datetime
import logging

from grain_chat_engine import GrainChatEngine

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="粮库智能对话API",
    description="基于自然语言的粮库数据分析系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局对话引擎实例
chat_engine = None

# 请求/响应模型
class ChatRequest(BaseModel):
    """聊天请求模型"""
    user_id: str
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    """聊天响应模型"""
    success: bool
    text: str
    charts: List[Dict[str, Any]] = []
    actions: List[Dict[str, Any]] = []
    intent: Optional[str] = None
    confidence: Optional[float] = None
    timestamp: datetime
    error: Optional[str] = None

class HistoryRequest(BaseModel):
    """历史记录请求模型"""
    user_id: str
    limit: int = 10
    offset: int = 0

class ReportRequest(BaseModel):
    """报告生成请求模型"""
    user_id: str
    report_type: str
    parameters: Dict[str, Any] = {}

class SystemStatus(BaseModel):
    """系统状态模型"""
    status: str
    version: str
    uptime: str
    active_users: int
    total_messages: int

# 依赖注入
def get_chat_engine() -> GrainChatEngine:
    """获取对话引擎实例"""
    global chat_engine
    if chat_engine is None:
        chat_engine = GrainChatEngine()
    return chat_engine

# API路由
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global chat_engine
    chat_engine = GrainChatEngine()
    logger.info("粮库智能对话API服务启动成功")

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "粮库智能对话API服务",
        "version": "1.0.0",
        "status": "running"
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, engine: GrainChatEngine = Depends(get_chat_engine)):
    """
    聊天对话接口
    
    接收用户消息，返回智能分析结果
    """
    try:
        logger.info(f"收到用户 {request.user_id} 的消息: {request.message}")
        
        # 处理消息
        result = engine.process_message(request.user_id, request.message)
        
        # 构建响应
        response = ChatResponse(
            success=result["success"],
            text=result["text"],
            charts=result.get("charts", []),
            actions=result.get("actions", []),
            intent=result.get("intent"),
            confidence=result.get("confidence"),
            timestamp=datetime.now(),
            error=result.get("error")
        )
        
        logger.info(f"成功处理用户 {request.user_id} 的消息")
        return response
        
    except Exception as e:
        logger.error(f"处理聊天消息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理消息失败: {str(e)}")

@app.get("/api/quick-questions")
async def get_quick_questions():
    """
    获取快捷问题列表
    """
    quick_questions = [
        "今天各仓库温度怎么样？",
        "库存还够几天？",
        "有哪些异常需要关注？",
        "生成今日温度报告",
        "最近一周的粮温变化趋势如何？",
        "哪个仓库的稻谷质量最好？"
    ]
    
    return {
        "success": True,
        "questions": quick_questions
    }

@app.post("/api/history")
async def get_chat_history(request: HistoryRequest):
    """
    获取聊天历史记录
    """
    try:
        # 这里应该从数据库获取历史记录
        # 目前返回模拟数据
        history = [
            {
                "id": 1,
                "user_id": request.user_id,
                "message": "今天1号仓库温度怎么样？",
                "response": "1号仓库今日平均温度18.5°C，比昨日下降0.3°C，温度范围17.2°C-19.8°C，整体稳定，无异常。",
                "timestamp": "2024-01-15 14:30:00",
                "intent": "temperature_query"
            },
            {
                "id": 2,
                "user_id": request.user_id,
                "message": "库存还有多少？",
                "response": "当前库存总量22000吨，包含4种粮食。稻谷5000吨，小麦8000吨，玉米6000吨，大豆3000吨。",
                "timestamp": "2024-01-15 14:25:00",
                "intent": "inventory_query"
            }
        ]
        
        # 分页处理
        total = len(history)
        start = request.offset
        end = start + request.limit
        paginated_history = history[start:end]
        
        return {
            "success": True,
            "data": paginated_history,
            "total": total,
            "limit": request.limit,
            "offset": request.offset
        }
        
    except Exception as e:
        logger.error(f"获取历史记录时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取历史记录失败: {str(e)}")

@app.post("/api/generate-report")
async def generate_report(request: ReportRequest, engine: GrainChatEngine = Depends(get_chat_engine)):
    """
    生成分析报告
    """
    try:
        logger.info(f"用户 {request.user_id} 请求生成 {request.report_type} 报告")
        
        # 根据报告类型生成相应的查询消息
        report_messages = {
            "temperature": "生成今日温度分析报告",
            "inventory": "生成库存状况报告", 
            "quality": "生成质量检测报告",
            "comprehensive": "生成综合分析报告"
        }
        
        message = report_messages.get(request.report_type, "生成综合分析报告")
        
        # 处理报告生成请求
        result = engine.process_message(request.user_id, message)
        
        if result["success"]:
            # 生成报告文件
            report_content = {
                "title": f"{request.report_type.upper()}分析报告",
                "generated_time": datetime.now().isoformat(),
                "user_id": request.user_id,
                "content": result["text"],
                "charts": result.get("charts", []),
                "data": result.get("data", {})
            }
            
            # 保存报告文件
            report_filename = f"report_{request.report_type}_{request.user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = f"reports/{report_filename}"
            
            # 确保目录存在
            os.makedirs("reports", exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report_content, f, ensure_ascii=False, indent=2, default=str)
            
            return {
                "success": True,
                "message": "报告生成成功",
                "report_id": report_filename,
                "download_url": f"/api/download-report/{report_filename}",
                "content": result["text"]
            }
        else:
            raise HTTPException(status_code=500, detail="报告生成失败")
            
    except Exception as e:
        logger.error(f"生成报告时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成报告失败: {str(e)}")

@app.get("/api/download-report/{report_id}")
async def download_report(report_id: str):
    """
    下载报告文件
    """
    try:
        report_path = f"reports/{report_id}"
        
        if not os.path.exists(report_path):
            raise HTTPException(status_code=404, detail="报告文件不存在")
        
        return FileResponse(
            path=report_path,
            filename=report_id,
            media_type='application/json'
        )
        
    except Exception as e:
        logger.error(f"下载报告时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下载报告失败: {str(e)}")

@app.get("/api/system/status", response_model=SystemStatus)
async def get_system_status():
    """
    获取系统状态
    """
    try:
        # 这里应该从实际的监控系统获取数据
        status = SystemStatus(
            status="healthy",
            version="1.0.0",
            uptime="2天3小时45分钟",
            active_users=15,
            total_messages=1247
        )
        
        return status
        
    except Exception as e:
        logger.error(f"获取系统状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")

@app.get("/api/analytics/dashboard")
async def get_dashboard_data():
    """
    获取仪表板数据
    """
    try:
        # 模拟仪表板数据
        dashboard_data = {
            "overview": {
                "total_warehouses": 12,
                "total_inventory": 45600,  # 吨
                "avg_temperature": 18.5,   # 摄氏度
                "alert_count": 3
            },
            "temperature_trend": [
                {"time": "00:00", "value": 18.2},
                {"time": "04:00", "value": 17.8},
                {"time": "08:00", "value": 18.5},
                {"time": "12:00", "value": 19.2},
                {"time": "16:00", "value": 19.0},
                {"time": "20:00", "value": 18.7}
            ],
            "inventory_distribution": [
                {"name": "稻谷", "value": 15600},
                {"name": "小麦", "value": 12800},
                {"name": "玉米", "value": 10200},
                {"name": "大豆", "value": 7000}
            ],
            "recent_alerts": [
                {
                    "id": 1,
                    "type": "temperature",
                    "message": "3号仓库温度偏高",
                    "timestamp": "2024-01-15 14:30:00",
                    "level": "warning"
                },
                {
                    "id": 2,
                    "type": "inventory",
                    "message": "大豆库存不足",
                    "timestamp": "2024-01-15 13:45:00",
                    "level": "info"
                }
            ]
        }
        
        return {
            "success": True,
            "data": dashboard_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取仪表板数据时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """
    健康检查接口
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "grain-chat-api"
    }

# 错误处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "服务器内部错误",
            "detail": str(exc) if app.debug else "请联系系统管理员"
        }
    )

# 启动配置
if __name__ == "__main__":
    # 创建必要的目录
    os.makedirs("reports", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    # 启动服务
    uvicorn.run(
        "grain_chat_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,  # 开发模式
        log_level="info"
    )
