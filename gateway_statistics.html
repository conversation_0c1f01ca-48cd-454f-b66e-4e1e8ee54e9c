<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物联网网关系统 - 数据统计分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 32px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .stat-card h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-value.success {
            color: #4caf50;
        }
        
        .stat-value.warning {
            color: #ff9800;
        }
        
        .stat-value.error {
            color: #f44336;
        }
        
        .chart-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .chart-card h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 16px;
            border: 2px dashed #dee2e6;
        }
        
        .details-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .details-section h3 {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .filter-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
        }
        
        .filter-input, .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .filter-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        .filter-button:hover {
            background: #5a6fd8;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
        }
        
        .status-online {
            color: #4caf50;
            font-weight: bold;
        }
        
        .status-offline {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #9e9e9e;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ff9800;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .chart-section {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-item {
                justify-content: space-between;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>物联网网关系统数据统计分析</h1>
            <p>实时监控网关设备状态、告警信息及系统运行情况</p>
        </div>
        
        <!-- 统计概览 -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3>设备状态分布</h3>
                <div class="stat-item">
                    <span class="stat-label">设备总数</span>
                    <span class="stat-value">1,248</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">在线设备</span>
                    <span class="stat-value success">1,156</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">离线设备</span>
                    <span class="stat-value warning">67</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">未激活设备</span>
                    <span class="stat-value">15</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已禁用设备</span>
                    <span class="stat-value">10</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>网关运行状态</h3>
                <div class="stat-item">
                    <span class="stat-label">标准网关总数</span>
                    <span class="stat-value">156</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">在线网关</span>
                    <span class="stat-value success">142</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">离线网关</span>
                    <span class="stat-value warning">12</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">未激活网关</span>
                    <span class="stat-value">2</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>设备类型分布</h3>
                <div class="stat-item">
                    <span class="stat-label">设备类总数</span>
                    <span class="stat-value">56</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">传感器类设备</span>
                    <span class="stat-value">678</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">控制器类设备</span>
                    <span class="stat-value">342</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">网关类设备</span>
                    <span class="stat-value">156</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">其他类设备</span>
                    <span class="stat-value">72</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>系统配置统计</h3>
                <div class="stat-item">
                    <span class="stat-label">驱动总数</span>
                    <span class="stat-value">78</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">驱动实例总数</span>
                    <span class="stat-value">246</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">告警规则数</span>
                    <span class="stat-value">45</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">联动规则数</span>
                    <span class="stat-value">23</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">通知规则数</span>
                    <span class="stat-value">18</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>数据流量统计</h3>
                <div class="stat-item">
                    <span class="stat-label">今日消息总数</span>
                    <span class="stat-value">125,678</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">上行消息</span>
                    <span class="stat-value success">98,234</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">下行消息</span>
                    <span class="stat-value">27,444</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">平均响应时间</span>
                    <span class="stat-value">125ms</span>
                </div>
            </div>

            <div class="stat-card">
                <h3>告警事件统计</h3>
                <div class="stat-item">
                    <span class="stat-label">今日告警总数</span>
                    <span class="stat-value">89</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">设备离线告警</span>
                    <span class="stat-value warning">34</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">通信异常告警</span>
                    <span class="stat-value error">23</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">数据异常告警</span>
                    <span class="stat-value warning">18</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">自定义告警</span>
                    <span class="stat-value">14</span>
                </div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="chart-section">
            <div class="chart-card">
                <h3>设备在线率趋势</h3>
                <div class="chart-placeholder">
                    📊 7天设备在线率变化趋势<br>
                    (基于设备状态：在线/离线/未激活/禁用)
                </div>
            </div>

            <div class="chart-card">
                <h3>消息流量分析</h3>
                <div class="chart-placeholder">
                    📈 上行/下行消息数量趋势<br>
                    (基于系统消息日志统计)
                </div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-card">
                <h3>设备类型分布</h3>
                <div class="chart-placeholder">
                    🥧 各设备类占比分析<br>
                    (传感器/控制器/网关/其他)
                </div>
            </div>

            <div class="chart-card">
                <h3>网关工作时长统计</h3>
                <div class="chart-placeholder">
                    📊 各网关累计工作时长排行<br>
                    (基于网关运行信息统计)
                </div>
            </div>
        </div>
        
        <!-- 详细数据列表 -->
        <div class="details-section">
            <h3>系统运行详情</h3>

            <!-- 筛选条件 -->
            <div class="filter-section">
                <div class="filter-row">
                    <div class="filter-item">
                        <span class="filter-label">数据类型：</span>
                        <select class="filter-select" id="dataTypeSelect">
                            <option value="devices">设备信息</option>
                            <option value="gateways">网关信息</option>
                            <option value="drivers">驱动实例</option>
                            <option value="messages">消息日志</option>
                            <option value="alarms">告警记录</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <span class="filter-label">状态筛选：</span>
                        <select class="filter-select">
                            <option>全部</option>
                            <option>在线</option>
                            <option>离线</option>
                            <option>未激活</option>
                            <option>已禁用</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <span class="filter-label">关键字：</span>
                        <input type="text" class="filter-input" placeholder="设备名称/网关ID/驱动名称">
                    </div>
                    <div class="filter-item">
                        <span class="filter-label">时间范围：</span>
                        <input type="date" class="filter-input">
                        <span>至</span>
                        <input type="date" class="filter-input">
                    </div>
                    <button class="filter-button">查询</button>
                    <button class="filter-button">导出Excel</button>
                </div>
            </div>
            
            <!-- 数据表格 -->
            <div id="deviceTable">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>设备类</th>
                            <th>所属网关</th>
                            <th>设备状态</th>
                            <th>最后通信时间</th>
                            <th>消息数量(今日)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DEV001</td>
                            <td>温度传感器-01</td>
                            <td>环境监测类</td>
                            <td>GW001</td>
                            <td><span class="status-online">在线</span></td>
                            <td>2024-01-15 14:30:25</td>
                            <td>1,234</td>
                            <td><a href="#">功能监测</a> | <a href="#">在线调试</a></td>
                        </tr>
                        <tr>
                            <td>DEV002</td>
                            <td>湿度传感器-01</td>
                            <td>环境监测类</td>
                            <td>GW001</td>
                            <td><span class="status-online">在线</span></td>
                            <td>2024-01-15 14:28:15</td>
                            <td>987</td>
                            <td><a href="#">功能监测</a> | <a href="#">在线调试</a></td>
                        </tr>
                        <tr>
                            <td>DEV003</td>
                            <td>压力控制器-01</td>
                            <td>控制执行类</td>
                            <td>GW002</td>
                            <td><span class="status-offline">离线</span></td>
                            <td>2024-01-15 12:25:42</td>
                            <td>0</td>
                            <td><a href="#">功能监测</a> | <a href="#">在线调试</a></td>
                        </tr>
                        <tr>
                            <td>DEV004</td>
                            <td>流量计-01</td>
                            <td>计量监测类</td>
                            <td>GW002</td>
                            <td><span class="status-online">在线</span></td>
                            <td>2024-01-15 14:15:30</td>
                            <td>2,156</td>
                            <td><a href="#">功能监测</a> | <a href="#">在线调试</a></td>
                        </tr>
                        <tr>
                            <td>DEV005</td>
                            <td>电机控制器-01</td>
                            <td>控制执行类</td>
                            <td>GW003</td>
                            <td><span class="status-disabled">已禁用</span></td>
                            <td>2024-01-14 16:45:18</td>
                            <td>0</td>
                            <td><a href="#">启用设备</a> | <a href="#">编辑</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="gatewayTable" style="display:none;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>网关ID</th>
                            <th>网关名称</th>
                            <th>网关类型</th>
                            <th>IP地址</th>
                            <th>运行状态</th>
                            <th>工作时长</th>
                            <th>子设备数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>GW001</td>
                            <td>标准网关-01</td>
                            <td>标准网关</td>
                            <td>*************</td>
                            <td><span class="status-online">在线</span></td>
                            <td>720小时</td>
                            <td>15</td>
                            <td><a href="#">配置</a> | <a href="#">重启</a></td>
                        </tr>
                        <tr>
                            <td>GW002</td>
                            <td>标准网关-02</td>
                            <td>标准网关</td>
                            <td>192.168.1.101</td>
                            <td><span class="status-online">在线</span></td>
                            <td>680小时</td>
                            <td>12</td>
                            <td><a href="#">配置</a> | <a href="#">重启</a></td>
                        </tr>
                        <tr>
                            <td>GW003</td>
                            <td>标准网关-03</td>
                            <td>标准网关</td>
                            <td>192.168.1.102</td>
                            <td><span class="status-offline">离线</span></td>
                            <td>156小时</td>
                            <td>8</td>
                            <td><a href="#">配置</a> | <a href="#">重启</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="driverTable" style="display:none;">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>驱动实例ID</th>
                            <th>驱动名称</th>
                            <th>所属网关</th>
                            <th>运行状态</th>
                            <th>关联设备数</th>
                            <th>启动时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DRV001</td>
                            <td>Modbus TCP驱动</td>
                            <td>GW001</td>
                            <td><span class="status-online">运行中</span></td>
                            <td>8</td>
                            <td>2024-01-10 09:00:00</td>
                            <td><a href="#">停止</a> | <a href="#">配置</a></td>
                        </tr>
                        <tr>
                            <td>DRV002</td>
                            <td>OPC UA驱动</td>
                            <td>GW002</td>
                            <td><span class="status-online">运行中</span></td>
                            <td>5</td>
                            <td>2024-01-12 14:30:00</td>
                            <td><a href="#">停止</a> | <a href="#">配置</a></td>
                        </tr>
                        <tr>
                            <td>DRV003</td>
                            <td>串口通信驱动</td>
                            <td>GW003</td>
                            <td><span class="status-offline">已停止</span></td>
                            <td>3</td>
                            <td>-</td>
                            <td><a href="#">启动</a> | <a href="#">配置</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟实时数据更新
        function updateStats() {
            // 这里可以添加AJAX请求来获取实时数据
            console.log('更新统计数据...');
        }

        // 每30秒更新一次数据
        setInterval(updateStats, 30000);

        // 数据类型切换功能
        document.getElementById('dataTypeSelect').addEventListener('change', function() {
            const selectedType = this.value;

            // 隐藏所有表格
            document.getElementById('deviceTable').style.display = 'none';
            document.getElementById('gatewayTable').style.display = 'none';
            document.getElementById('driverTable').style.display = 'none';

            // 显示选中的表格
            switch(selectedType) {
                case 'devices':
                    document.getElementById('deviceTable').style.display = 'block';
                    break;
                case 'gateways':
                    document.getElementById('gatewayTable').style.display = 'block';
                    break;
                case 'drivers':
                    document.getElementById('driverTable').style.display = 'block';
                    break;
                case 'messages':
                    // 可以添加消息日志表格
                    alert('消息日志功能开发中...');
                    break;
                case 'alarms':
                    // 可以添加告警记录表格
                    alert('告警记录功能开发中...');
                    break;
            }
        });

        // 筛选功能
        document.querySelector('.filter-button').addEventListener('click', function() {
            // 这里添加筛选逻辑
            console.log('执行筛选...');
        });

        // 导出功能
        document.querySelectorAll('.filter-button')[1].addEventListener('click', function() {
            // 这里添加导出逻辑
            console.log('导出数据...');
            alert('导出功能开发中...');
        });
    </script>
</body>
</html>
