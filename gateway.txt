系统功能	功能模块	功能项	描述
设备概况	　	显示	支持全屏显示设备集成整体状况。
		查看	支持查看所有接入设备的实时状况（设备总数、当前在线设备数、已禁用设备数、故障设备数和报警设备数）。
			支持查看所有接入设备的历史状况及趋势，可自定义组合设备类型和记录时长查看历史记录。
设备管理（可选）	设备模型库	查询	支持分页、列表查看设备模型详情及功能（属性、事件、服务），支持基于模型名称进行模糊搜索。
	设备类	查询	支持分页、列表查看设备类详情及功能（属性、事件、服务），支持基于设备类名称进行模糊搜索。
		新增	支持用户自定义新增设备类。
		编辑	支持用户修改设备类名称、设备类分组和备注。
		删除	支持用户删除设备类。对于已实例化设备的设备类，需先删除该设备类下所有设备后才可删除。
		功能定义	支持快速添加设备模型库的标准设备模型及其功能。
			支持新增、编辑、删除设备类功能，保存发布后生效（设备类的功能定义变化时将会影响到已实例化的设备配置，需要谨慎操作）。
			完成功能定义并发布生效后，可以查看设备类的物模型（设备属性建模），同时支持导出完整物模型，用于操作系统应用或 SDK 开发。
			支持恢复设备类历史版本的功能定义，当恢复历史版本，已实例化的设备将同步恢复设备功能（会影响到已实例化的设备配置，需要谨慎操作）。
			支持功能定义编辑状态下，导入新的物模型及导出历史版本物模型 TSL。
			支持后续继续编辑数据解析脚本内容，重新提交生效。
			支持删除脚本内容。删除脚本内容后，系统将不再调用脚本内容解析。
		Topic 列表	支持设备向操作系统上报数据的 Topic。
			支持操作系统下发数据到设备的 Topic。
			设备类实例化设备后，设备继承设备类的所有 Topic。
		设备认证	支持 网关设备身份认证，确保设备安全。
	设备	查看	支持分页、列表查看所有设备详情，支持模糊搜索。
			支持批量导入/出设备信息进行查看。
			支持查看设备状态（禁用、未激活、离线、在线）。
			设备详情页面显示设备基础信息和物模型功能信息。
		新增	支持单条新增设备。
			支持通过 excel 快速编辑，再导入到系统完成批量设备新增。
		修改	支持用户修改设备详情。
			支持通过 excel 快速编辑，再导入到系统完成批量设备修改。
			支持在树形设备列表修改设备类分组名称和设备类名称。
		删除	支持单条删除设备。
			支持批量删除设备。
		启/禁用	支持禁用/启用单个设备。
			支持批量禁用/启用设备。
		功能监测	支持查看设备功能当前状态及历史记录。编辑可写入的属性，调用服务、查看上报事件。
		状态上报	支持应用订阅实时推送的设备状态。
		Topic 列表	支持实例化设备继承设备类的所有 Topic。
		在线调试	支持设备在线状态时，使用在线调试功能，从系统下发指令给设备端进行功能测试。
			支持关联当前设备的日志消息。
		子设备	子设备本质是设备。但子设备不直接连接操作系统，需要通过网关设备与操作系统连接。
			支持网关设备下挂载子设备或移除子设备。
		设备分组	支持自定义添加设备分组。
			支持批量删除组内设备。
			支持模糊搜索分组名称。
		网关设备	支持查看标准网关设备的固件名称和版本号、IP地址、网关类型、网关型号、串口名称和起始编号。
			支持单个新增和批量导入网关设备。
			支持在标准网关设备页面进行子设备配置、驱动配置和下发配置及固件（第三方网关只支持子设备配置）。
			支持批量导入/出子设备功能配置。 
			支持显示下发配置及固件的实时进度。
网关管理	标准网关 	查看	支持分页、列表查看系统内所有的标准网关信息，支持模糊搜索。
			支持查看标准网关的的运行信息，例如：启用状态、激活状态、在线状态、工作时长。
			支持远程下发网关配置及固件、远程重启网关。
		修改	支持用户修改网关的标签信息、连接参数、网关内驱动实例参数。
		删除	支持用户删除标准网关。
			若网关内已有驱动实例运行，将不允许删除。
		子设备管理	支持添加子设备，查看当前网关挂载的子设备、移除子设备。
			支持子设备关联驱动实例、配置功能属性。
			支持批量导入子设备功能配置。
		驱动实例	支持分页、列表查看网关内所有的驱动实例信息。
			支持查看驱动实例的运行信息。
			支持远程启动、停止驱动实例。
			支持查看驱动实例功能与设备功能的关联关系。
			支持定制驱动对应的设备类自动绑点功能。
			支持新增、修改、删除驱动实例，但不允许删除已被设备实例绑定的驱动实例。
		关联本地网关	支持跳转连接至该网关对应的本地网关 web 页面并进行配置。
	驱动管理	查看	支持分页、列表查看系统内所有的驱动详情（驱动名称、驱动 ID 等），支持模糊搜索。
			可下载驱动文件到本地。
		新增	用户可基于 SDK 开发驱动，开发完成后可以上传驱动 JAR 包和描述文件，完成驱动新增，供后续使用。
		更新	支持用户通过更新驱动版本号的方式，更新系统内的驱动。更新后，需要重新配置网关驱动并下发配置，以生效网关配置。
		删除	支持用户删除驱动，若该驱动下已有驱动实例，驱动先删除所有驱动实例，才可删除驱动。删除后，数据清除不可恢复，用户需要谨慎操作。
规则引擎	告警中心	历史记录	告警中心列表（设备类、设备名称、告警类型、告警详情、发生时间）分页查看，实时刷新最新告警。
			支持按照时间、告警类型、设备类别查询告警记录。
			告警记录支持导出为 excel 表格文件。
			可查看告警详情。
		告警规则	支持按照（规则名称、描述、设备类、设备实例、功能、告警类型、告警延时、使用状态、操作）字段查看自定义告警列表。
			支持按照设备类、告警类型、规则名称筛选查询告警。
			支持批量导入导出快速新增及更新自定义告警。
			支持删除自定义告警。
		通知规则	支持以短信或邮件方式将设备原生功能、自定义告警通知用户，同时支持第三方应用订阅告警。
			支持列表查看通知规则，模糊搜索通知规则。
			支持用户自定义新增、编辑、删除通知规则。
	联动管理	时间触发	触发条件为时间点，即到了时间点就执行设备动作或脚本预案。
		设备触发	设备的原生事件、属性、也可以是自定义告警触发执行设备动作或脚本预案。
		查看	支持查看联动分页列表，支持模糊搜索。
		新增	支持新增联动。
		编辑	支持编辑修改联动。
		删除	支持删除联动。
		快速启停	支持在联动列表快速启停联动，当告警规则内涉及的设备被删除后，联动列表使用状态列提示该条联动”已失效“。
	数据过滤	查看	支持查看数据过滤规则。
		修改	支持配置数据过滤规则，并对规则进行测试。
运维监控	在线调试	调试	支持使用在线调试功能，从操作系统下发指令给设备端进行功能测试。
			支持在线调试上下行通信数据，确定设备类、协议、解析脚本功能是否正确。
		数据追踪	支持设备数据主动上报；应用下发数据到物理设备及物理设备返回结果。
		关联日志	支持通过查看日志记录功能，可快捷进入当前设备的日志服务信息页面
	日志服务	消息日志	支持按照时间、设备ID、消息类型、消息内容关键字筛选查询。
			支持查看状态码对应表，进一步查明状态信息。
		联动日志	支持按照联动名称、时间筛选查询。
日志服务	　	查看	展示网关运行时产生的日志文件。如：网关平台运行日志、驱动运行日志、与应用交互连接日志等。
		下载	支持用户直接查看文件及下载日志文件，查看日志详情。
系统配置	基础配置	查看	支持查看当前网关的基础信息，即网关身份三元组信息。
		修改	支持用户基于系统侧创建的网关设备，修改网关身份三元组信息。
	网络配置	查看	支持查看当前网关各网卡的网络配置信息，包括 IP 地址，子网掩码、网关地址、DNS。
		修改	支持用户基于实际配置情况，修改当前网卡的网络配置。
	系统地址	查看	支持查看系统地址信息，即当前网关连接的物联网操作系统 IP 地址及端口号。
		修改	支持用户基于实际情况，修改当前网关所连物联网操作系统的 IP 地址及端口号。
	配置同步	更新	支持将系统侧当前的所有驱动同步至网关侧（硬件网关）。
用户管理	修改密码	修改	支持用户修改网关web页面的登录密码。