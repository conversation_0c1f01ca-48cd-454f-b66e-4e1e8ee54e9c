<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备接入管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #1976d2;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1565c0;
        }
        
        .btn-success {
            background: #4caf50;
            color: white;
        }
        
        .btn-success:hover {
            background: #45a049;
        }
        
        .btn-warning {
            background: #ff9800;
            color: white;
        }
        
        .btn-warning:hover {
            background: #f57c00;
        }
        
        .btn-danger {
            background: #f44336;
            color: white;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .main-content {
            display: flex;
            gap: 20px;
        }
        
        .sidebar {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .sidebar h3 {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 8px;
        }
        
        .device-tree {
            list-style: none;
        }
        
        .tree-item {
            margin-bottom: 8px;
        }
        
        .tree-node {
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tree-node:hover {
            background: #f5f5f5;
        }
        
        .tree-node.active {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .device-count {
            font-size: 12px;
            background: #e0e0e0;
            padding: 2px 6px;
            border-radius: 10px;
        }
        
        .content-area {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .search-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .search-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
        }
        
        .search-input, .search-select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .device-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .device-table th,
        .device-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }
        
        .device-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
        }
        
        .device-table tr:hover {
            background: #f5f5f5;
        }
        
        .status-online {
            color: #4caf50;
            font-weight: bold;
        }
        
        .status-offline {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #9e9e9e;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #ff9800;
            font-weight: bold;
        }
        
        .action-links {
            display: flex;
            gap: 8px;
        }
        
        .action-links a {
            color: #1976d2;
            text-decoration: none;
            font-size: 12px;
        }
        
        .action-links a:hover {
            text-decoration: underline;
        }
        
        .checkbox {
            margin-right: 8px;
        }
        
        .batch-actions {
            margin-bottom: 15px;
            padding: 10px;
            background: #fff3e0;
            border-radius: 4px;
            display: none;
        }
        
        .batch-actions.show {
            display: block;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            gap: 10px;
        }
        
        .pagination button {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .pagination button:hover {
            background: #f5f5f5;
        }
        
        .pagination button.active {
            background: #1976d2;
            color: white;
            border-color: #1976d2;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .close {
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close:hover {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-textarea {
            height: 80px;
            resize: vertical;
        }
        
        /* 统计分析样式 */
        .stats-overview {
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 32px;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 50%;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .trend-up {
            color: #4caf50;
            font-weight: bold;
        }

        .trend-down {
            color: #f44336;
            font-weight: bold;
        }

        .trend-stable {
            color: #9e9e9e;
            font-weight: bold;
        }

        .trend-text {
            font-size: 12px;
            color: #999;
        }



        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .search-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-item {
                justify-content: space-between;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            }


        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>设备接入管理</h1>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openAddDeviceModal()">+ 新增设备</button>
                <button class="btn btn-success" onclick="openBatchImportModal()">批量导入</button>
                <button class="btn btn-secondary" onclick="exportDevices()">导出设备</button>
                <button class="btn btn-warning" onclick="openDeviceGroupModal()">设备分组</button>
            </div>
        </div>

        <!-- 统计分析区域 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">📱</div>
                    <div class="stat-content">
                        <div class="stat-number">1,248</div>
                        <div class="stat-label">设备总数</div>
                        <div class="stat-trend">
                            <span class="trend-up">↗ +23</span>
                            <span class="trend-text">本周新增</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🟢</div>
                    <div class="stat-content">
                        <div class="stat-number">92.6%</div>
                        <div class="stat-label">设备在线率</div>
                        <div class="stat-trend">
                            <span class="trend-up">↗ +1.2%</span>
                            <span class="trend-text">较昨日</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">⚡</div>
                    <div class="stat-content">
                        <div class="stat-number">156</div>
                        <div class="stat-label">活跃网关</div>
                        <div class="stat-trend">
                            <span class="trend-stable">→ 0</span>
                            <span class="trend-text">无变化</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number">125.6K</div>
                        <div class="stat-label">今日消息数</div>
                        <div class="stat-trend">
                            <span class="trend-up">↗ +8.5%</span>
                            <span class="trend-text">较昨日</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">🔧</div>
                    <div class="stat-content">
                        <div class="stat-number">15</div>
                        <div class="stat-label">待激活设备</div>
                        <div class="stat-trend">
                            <span class="trend-down">↘ -5</span>
                            <span class="trend-text">较昨日</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <div class="stat-number">67</div>
                        <div class="stat-label">离线设备</div>
                        <div class="stat-trend">
                            <span class="trend-down">↘ -3</span>
                            <span class="trend-text">较昨日</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        


        <div class="main-content">
            <!-- 左侧设备分组树 -->
            <div class="sidebar">
                <h3>设备分组</h3>
                <ul class="device-tree">
                    <li class="tree-item">
                        <div class="tree-node active" onclick="filterByGroup('all')">
                            <span>全部设备</span>
                            <span class="device-count">1,248</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByGroup('sensor')">
                            <span>传感器类</span>
                            <span class="device-count">678</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByGroup('controller')">
                            <span>控制器类</span>
                            <span class="device-count">342</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByGroup('gateway')">
                            <span>网关设备</span>
                            <span class="device-count">156</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByGroup('meter')">
                            <span>计量设备</span>
                            <span class="device-count">72</span>
                        </div>
                    </li>
                </ul>

                <h3 style="margin-top: 30px;">设备状态</h3>
                <ul class="device-tree">
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByStatus('online')">
                            <span>在线设备</span>
                            <span class="device-count">1,156</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByStatus('offline')">
                            <span>离线设备</span>
                            <span class="device-count">67</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByStatus('inactive')">
                            <span>未激活设备</span>
                            <span class="device-count">15</span>
                        </div>
                    </li>
                    <li class="tree-item">
                        <div class="tree-node" onclick="filterByStatus('disabled')">
                            <span>已禁用设备</span>
                            <span class="device-count">10</span>
                        </div>
                    </li>
                </ul>


            </div>
            
            <!-- 右侧内容区域 -->
            <div class="content-area">
                <!-- 搜索筛选区域 -->
                <div class="search-section">
                    <div class="search-row">
                        <div class="search-item">
                            <span class="search-label">设备名称：</span>
                            <input type="text" class="search-input" placeholder="支持模糊搜索">
                        </div>
                        <div class="search-item">
                            <span class="search-label">设备类：</span>
                            <select class="search-select">
                                <option>全部</option>
                                <option>环境监测类</option>
                                <option>控制执行类</option>
                                <option>计量监测类</option>
                                <option>网关通信类</option>
                            </select>
                        </div>
                        <div class="search-item">
                            <span class="search-label">所属网关：</span>
                            <select class="search-select">
                                <option>全部</option>
                                <option>GW001</option>
                                <option>GW002</option>
                                <option>GW003</option>
                            </select>
                        </div>
                        <button class="btn btn-primary">查询</button>
                        <button class="btn btn-secondary">重置</button>
                    </div>
                </div>
                
                <!-- 批量操作区域 -->
                <div class="batch-actions" id="batchActions">
                    <span>已选择 <span id="selectedCount">0</span> 个设备</span>
                    <button class="btn btn-success" onclick="batchEnable()">批量启用</button>
                    <button class="btn btn-warning" onclick="batchDisable()">批量禁用</button>
                    <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    <button class="btn btn-secondary" onclick="clearSelection()">取消选择</button>
                </div>
                
                <!-- 设备列表表格 -->
                <table class="device-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>设备类</th>
                            <th>所属网关</th>
                            <th>设备状态</th>
                            <th>接入时间</th>
                            <th>最后通信时间</th>
                            <th>消息数(今日)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="deviceTableBody">
                        <!-- 设备数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <button onclick="changePage('prev')">上一页</button>
                    <button class="active">1</button>
                    <button onclick="changePage(2)">2</button>
                    <button onclick="changePage(3)">3</button>
                    <button onclick="changePage('next')">下一页</button>
                    <span style="margin-left: 20px;">共 1,248 条记录，每页 20 条</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增设备模态框 -->
    <div id="addDeviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增设备</h3>
                <span class="close" onclick="closeModal('addDeviceModal')">&times;</span>
            </div>
            <form id="addDeviceForm">
                <div class="form-group">
                    <label class="form-label">设备名称 *</label>
                    <input type="text" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">设备类 *</label>
                    <select class="form-select" required>
                        <option>请选择设备类</option>
                        <option>环境监测类</option>
                        <option>控制执行类</option>
                        <option>计量监测类</option>
                        <option>网关通信类</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">所属网关 *</label>
                    <select class="form-select" required>
                        <option>请选择网关</option>
                        <option>GW001 - 标准网关-01</option>
                        <option>GW002 - 标准网关-02</option>
                        <option>GW003 - 标准网关-03</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">设备描述</label>
                    <textarea class="form-textarea" placeholder="请输入设备描述信息"></textarea>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addDeviceModal')">取消</button>
                    <button type="submit" class="btn btn-primary">确定</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 示例设备数据
        const deviceData = [
            {
                id: 'DEV001',
                name: '温度传感器-01',
                deviceClass: '环境监测类',
                gateway: 'GW001',
                status: 'online',
                connectTime: '2024-01-10 09:15:30',
                lastComm: '2024-01-15 14:30:25',
                messageCount: 1234
            },
            {
                id: 'DEV002',
                name: '湿度传感器-01',
                deviceClass: '环境监测类',
                gateway: 'GW001',
                status: 'online',
                connectTime: '2024-01-10 09:18:45',
                lastComm: '2024-01-15 14:28:15',
                messageCount: 987
            },
            {
                id: 'DEV003',
                name: '压力控制器-01',
                deviceClass: '控制执行类',
                gateway: 'GW002',
                status: 'offline',
                connectTime: '2024-01-12 14:22:10',
                lastComm: '2024-01-15 12:25:42',
                messageCount: 0
            },
            {
                id: 'DEV004',
                name: '流量计-01',
                deviceClass: '计量监测类',
                gateway: 'GW002',
                status: 'online',
                connectTime: '2024-01-11 16:30:15',
                lastComm: '2024-01-15 14:15:30',
                messageCount: 2156
            },
            {
                id: 'DEV005',
                name: '电机控制器-01',
                deviceClass: '控制执行类',
                gateway: 'GW003',
                status: 'disabled',
                connectTime: '2024-01-08 11:45:20',
                lastComm: '2024-01-14 16:45:18',
                messageCount: 0
            },
            {
                id: 'DEV006',
                name: '光照传感器-01',
                deviceClass: '环境监测类',
                gateway: 'GW001',
                status: 'inactive',
                connectTime: '2024-01-15 10:20:30',
                lastComm: '-',
                messageCount: 0
            },
            {
                id: 'DEV007',
                name: '振动传感器-01',
                deviceClass: '环境监测类',
                gateway: 'GW002',
                status: 'online',
                connectTime: '2024-01-13 08:15:45',
                lastComm: '2024-01-15 14:32:10',
                messageCount: 856
            },
            {
                id: 'DEV008',
                name: '阀门控制器-01',
                deviceClass: '控制执行类',
                gateway: 'GW003',
                status: 'online',
                connectTime: '2024-01-09 15:30:25',
                lastComm: '2024-01-15 14:29:55',
                messageCount: 1567
            }
        ];
        
        // 渲染设备表格
        function renderDeviceTable(data = deviceData) {
            const tbody = document.getElementById('deviceTableBody');
            tbody.innerHTML = '';
            
            data.forEach(device => {
                const statusClass = `status-${device.status}`;
                const statusText = {
                    'online': '在线',
                    'offline': '离线',
                    'disabled': '已禁用',
                    'inactive': '未激活'
                }[device.status];

                const row = `
                    <tr>
                        <td><input type="checkbox" class="device-checkbox" value="${device.id}" onchange="updateBatchActions()"></td>
                        <td>${device.id}</td>
                        <td>${device.name}</td>
                        <td>${device.deviceClass}</td>
                        <td>${device.gateway}</td>
                        <td><span class="${statusClass}">${statusText}</span></td>
                        <td>${device.connectTime}</td>
                        <td>${device.lastComm}</td>
                        <td>${device.messageCount.toLocaleString()}</td>
                        <td>
                            <div class="action-links">
                                <a href="#" onclick="viewDevice('${device.id}')">详情</a>
                                <a href="#" onclick="editDevice('${device.id}')">编辑</a>
                                <a href="#" onclick="debugDevice('${device.id}')">在线调试</a>
                                <a href="#" onclick="toggleDevice('${device.id}')">${device.status === 'disabled' ? '启用' : '禁用'}</a>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
        
        // 更新批量操作显示
        function updateBatchActions() {
            const checkboxes = document.querySelectorAll('.device-checkbox:checked');
            const batchActions = document.getElementById('batchActions');
            const selectedCount = document.getElementById('selectedCount');
            
            if (checkboxes.length > 0) {
                batchActions.classList.add('show');
                selectedCount.textContent = checkboxes.length;
            } else {
                batchActions.classList.remove('show');
            }
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.device-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateBatchActions();
        }
        
        // 打开模态框
        function openAddDeviceModal() {
            document.getElementById('addDeviceModal').style.display = 'block';
        }
        
        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // 设备操作函数
        function viewDevice(deviceId) {
            alert(`查看设备详情: ${deviceId}`);
        }
        
        function editDevice(deviceId) {
            alert(`编辑设备: ${deviceId}`);
        }
        
        function debugDevice(deviceId) {
            alert(`在线调试设备: ${deviceId}`);
        }
        
        function toggleDevice(deviceId) {
            alert(`切换设备状态: ${deviceId}`);
        }
        
        // 批量操作函数
        function batchEnable() {
            const selected = document.querySelectorAll('.device-checkbox:checked');
            alert(`批量启用 ${selected.length} 个设备`);
        }
        
        function batchDisable() {
            const selected = document.querySelectorAll('.device-checkbox:checked');
            alert(`批量禁用 ${selected.length} 个设备`);
        }
        
        function batchDelete() {
            const selected = document.querySelectorAll('.device-checkbox:checked');
            if (confirm(`确定要删除选中的 ${selected.length} 个设备吗？`)) {
                alert(`删除 ${selected.length} 个设备`);
            }
        }
        
        function clearSelection() {
            document.querySelectorAll('.device-checkbox').forEach(cb => cb.checked = false);
            document.getElementById('selectAll').checked = false;
            updateBatchActions();
        }
        
        // 分组筛选
        function filterByGroup(group) {
            // 更新活动状态
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('按分组筛选:', group);
            updateDeviceStats(group);
        }

        function filterByStatus(status) {
            // 更新活动状态
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('按状态筛选:', status);
            updateDeviceStats(null, status);
        }

        function filterByGateway(gateway) {
            // 更新活动状态
            document.querySelectorAll('.tree-node').forEach(node => {
                node.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log('按网关筛选:', gateway);
            updateDeviceStats(null, null, gateway);
        }

        // 更新设备统计信息
        function updateDeviceStats(group = null, status = null, gateway = null) {
            // 模拟根据筛选条件更新统计数据
            let filteredData = deviceData;

            if (group && group !== 'all') {
                // 根据设备类型筛选
                const groupMap = {
                    'sensor': '环境监测类',
                    'controller': '控制执行类',
                    'gateway': '网关通信类',
                    'meter': '计量监测类'
                };
                filteredData = filteredData.filter(device =>
                    device.deviceClass === groupMap[group]
                );
            }

            if (status) {
                filteredData = filteredData.filter(device => device.status === status);
            }

            if (gateway && gateway !== 'others') {
                filteredData = filteredData.filter(device => device.gateway === gateway);
            }

            // 重新渲染表格
            renderDeviceTable(filteredData);

            // 更新统计卡片（这里可以添加实际的统计逻辑）
            console.log(`筛选后设备数量: ${filteredData.length}`);
        }
        
        // 其他功能函数
        function openBatchImportModal() {
            alert('批量导入功能开发中...');
        }
        
        function exportDevices() {
            alert('导出设备功能开发中...');
        }
        
        function openDeviceGroupModal() {
            alert('设备分组管理功能开发中...');
        }
        
        function changePage(page) {
            console.log('切换到页面:', page);
        }
        
        // 模拟实时数据更新
        function updateRealTimeStats() {
            // 模拟更新统计数据
            const stats = [
                { selector: '.stat-number', values: ['1,248', '92.6%', '156', '125.6K', '15', '67'] },
            ];

            // 这里可以添加AJAX请求来获取实时数据
            console.log('更新实时统计数据...');
        }

        // 每30秒更新一次统计数据
        setInterval(updateRealTimeStats, 30000);

        // 页面加载完成后渲染表格
        document.addEventListener('DOMContentLoaded', function() {
            renderDeviceTable();
            updateRealTimeStats();
        });
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
