# 粮库行业数据分析智能体

## 项目简介

这是一个专门为粮库行业设计的数据分析智能体系统，能够自动收集、分析粮库运营数据，提供智能化的决策支持和预警功能。

## 功能特点

### 🔍 数据分析功能
- **库存分析**: 实时库存监控、趋势分析、预测
- **质量分析**: 粮食质量评估、变化趋势、异常检测
- **环境分析**: 温湿度监控、环境预警、优化建议
- **综合分析**: 多维度数据关联分析、风险评估

### 📊 智能预警
- 环境参数异常预警
- 库存不足预警
- 质量下降预警
- 设备故障预警

### 📈 报告生成
- 自动生成日报、周报、月报
- 可视化图表展示
- 自定义分析报告
- 预警通知报告

### 🤖 智能建议
- 库存优化建议
- 环境调控建议
- 质量保持建议
- 成本控制建议

## 快速开始

### 1. 环境准备

```bash
# 安装Python依赖
pip install pandas numpy matplotlib seaborn plotly scikit-learn

# 或使用requirements.txt（如果有的话）
pip install -r requirements.txt
```

### 2. 运行智能体

```bash
# 直接运行主程序
python grain_ai_agent.py

# 或者使用启动脚本
python start_agent.py
```

### 3. 查看结果

分析结果会自动保存在以下目录：
- `data/results/reports/` - 分析报告
- `data/results/alerts/` - 预警信息
- `data/results/predictions/` - 预测结果
- `data/results/recommendations/` - 建议方案

## 目录结构

```
grain_storage_ai/
├── grain_ai_agent.py          # 主程序
├── start_agent.py             # 启动脚本
├── README.md                  # 说明文档
├── configs/
│   └── agent_config.json      # 配置文件
├── data/
│   ├── raw/                   # 原始数据
│   │   ├── inventory.csv      # 库存数据
│   │   ├── quality.csv        # 质量数据
│   │   ├── environment.csv    # 环境数据
│   │   └── market.csv         # 市场数据
│   ├── processed/             # 处理后数据
│   └── results/               # 分析结果
│       ├── reports/           # 报告文件
│       ├── alerts/            # 预警信息
│       ├── predictions/       # 预测结果
│       └── recommendations/   # 建议方案
├── models/                    # 机器学习模型
├── logs/                      # 系统日志
└── utils/                     # 工具函数
```

## 配置说明

### 数据源配置
在 `configs/agent_config.json` 中配置数据源：

```json
{
  "data_sources": {
    "inventory_file": "data/raw/inventory.csv",
    "quality_file": "data/raw/quality.csv",
    "environment_file": "data/raw/environment.csv"
  }
}
```

### 预警阈值配置
```json
{
  "analysis_config": {
    "alert_thresholds": {
      "temperature_max": 25.0,
      "humidity_max": 65.0,
      "inventory_min_days": 30,
      "quality_score_min": 80.0
    }
  }
}
```

## 数据格式说明

### 库存数据格式 (inventory.csv)
```csv
date,warehouse_id,grain_type,quantity,unit,batch_number,entry_date,quality_grade
2024-01-15,warehouse_001,rice,5000,tons,BATCH001,2023-10-15,A
```

### 环境数据格式 (environment.csv)
```csv
timestamp,warehouse_id,temperature,humidity,co2_level,pest_level,ventilation_status
2024-01-15 14:30:00,warehouse_001,18.5,58.2,400,0,active
```

### 质量数据格式 (quality.csv)
```csv
test_date,warehouse_id,grain_type,batch_number,moisture_content,protein_content,quality_score
2024-01-15,warehouse_001,rice,BATCH001,14.2,7.8,92
```

## 使用示例

### 1. 执行日常分析
```python
from grain_ai_agent import GrainStorageAIAgent

# 创建智能体
agent = GrainStorageAIAgent()

# 执行日常分析
success = agent.run_daily_analysis()
```

### 2. 生成自定义报告
```python
# 生成库存分析报告
report_path = agent.generate_custom_report("inventory_analysis")

# 生成质量趋势报告
report_path = agent.generate_custom_report("quality_trend", {
    "days": 30,
    "grain_type": "rice"
})
```

## 扩展开发

### 1. 添加新的数据源
在 `DataProcessor` 类中添加新的数据处理方法：

```python
def process_new_data_source(self, data_path):
    # 处理新数据源的逻辑
    pass
```

### 2. 添加新的分析算法
在 `GrainAnalyzer` 类中添加新的分析方法：

```python
def new_analysis_method(self, data):
    # 新的分析算法
    pass
```

### 3. 自定义报告模板
在 `ReportGenerator` 类中添加新的报告模板：

```python
def generate_custom_template(self, data):
    # 自定义报告生成逻辑
    pass
```

## 常见问题

### Q: 如何修改预警阈值？
A: 编辑 `configs/agent_config.json` 文件中的 `alert_thresholds` 部分。

### Q: 如何添加新的粮食品种？
A: 在配置文件的 `grain_types` 部分添加新品种的信息。

### Q: 如何查看系统日志？
A: 日志文件保存在 `logs/` 目录下，可以查看 `application/grain_ai.log`。

### Q: 如何备份分析结果？
A: 所有分析结果都保存在 `data/results/` 目录下，定期备份该目录即可。

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看日志文件获取错误信息
2. 检查配置文件是否正确
3. 确认数据格式是否符合要求
4. 联系技术支持团队

## 版本更新

### v1.0.0 (当前版本)
- 基础数据分析功能
- 自动报告生成
- 预警系统
- 配置化管理

### 计划功能
- 机器学习预测模型
- 实时数据流处理
- Web界面管理
- 移动端应用
- 第三方系统集成

---

*本项目持续更新中，欢迎提出建议和反馈！*
