<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>粮情报表 - 移动端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            color: #333;
            overflow-x: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .header h1 {
            font-size: 20px;
            font-weight: bold;
        }
        
        .user-info {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .header-stats {
            display: flex;
            justify-content: space-between;
            text-align: center;
        }
        
        .header-stat {
            flex: 1;
        }
        
        .header-stat-value {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .header-stat-label {
            font-size: 10px;
            opacity: 0.8;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 12px 8px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: white;
            font-size: 14px;
            transition: all 0.3s ease;
            border-right: 1px solid #eee;
        }
        
        .nav-tab:last-child {
            border-right: none;
        }
        
        .nav-tab.active {
            background: #667eea;
            color: white;
        }
        
        .container {
            padding: 0 10px 20px;
        }
        
        .report-content {
            display: none;
        }
        
        .report-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-content {
            padding: 15px;
        }
        
        .warehouse-grid {
            display: grid;
            gap: 10px;
        }
        
        .warehouse-item {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            border-left: 4px solid #ddd;
            position: relative;
        }
        
        .warehouse-item.normal {
            border-left-color: #4caf50;
        }
        
        .warehouse-item.warning {
            border-left-color: #ff9800;
        }
        
        .warehouse-item.danger {
            border-left-color: #f44336;
        }
        
        .warehouse-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .warehouse-name {
            font-weight: bold;
            font-size: 16px;
        }
        
        .warehouse-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 10px;
            color: white;
        }
        
        .status-normal {
            background: #4caf50;
        }
        
        .status-warning {
            background: #ff9800;
        }
        
        .status-danger {
            background: #f44336;
        }
        
        .warehouse-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            font-size: 14px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .detail-label {
            color: #666;
        }
        
        .detail-value {
            font-weight: bold;
        }
        
        .alert-list {
            list-style: none;
        }
        
        .alert-item {
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            font-size: 14px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .alert-urgent {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .alert-warning {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
        }
        
        .alert-normal {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .alert-icon {
            font-size: 16px;
            margin-top: 2px;
        }
        
        .schedule-list {
            list-style: none;
        }
        
        .schedule-item {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .schedule-time {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .schedule-tasks {
            list-style: none;
            font-size: 13px;
        }
        
        .schedule-tasks li {
            margin-bottom: 4px;
            padding-left: 12px;
            position: relative;
        }
        
        .schedule-tasks li::before {
            content: '•';
            position: absolute;
            left: 0;
            color: #667eea;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .floating-action {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #667eea;
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .floating-action:hover {
            transform: scale(1.1);
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .quick-stat {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 12px;
            text-align: center;
        }
        
        .quick-stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .quick-stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .trend-indicator {
            font-size: 12px;
            margin-left: 5px;
        }
        
        .trend-up {
            color: #f44336;
        }
        
        .trend-down {
            color: #4caf50;
        }
        
        .trend-stable {
            color: #666;
        }
        
        .swipe-hint {
            text-align: center;
            color: #999;
            font-size: 12px;
            margin: 10px 0;
        }
        
        /* 滑动动画 */
        .slide-container {
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .slide-content {
            display: flex;
            gap: 10px;
            padding: 0 10px;
        }
        
        .slide-item {
            min-width: 280px;
            scroll-snap-align: start;
        }
        
        /* 响应式调整 */
        @media (max-width: 360px) {
            .header {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 18px;
            }
            
            .nav-tab {
                padding: 10px 6px;
                font-size: 12px;
            }
            
            .card-content {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <h1>🌾 粮情报表</h1>
            <div class="user-info">
                👤 张三<br>
                <span id="currentTime">14:30</span>
            </div>
        </div>
        <div class="header-stats">
            <div class="header-stat">
                <div class="header-stat-value">3</div>
                <div class="header-stat-label">管理仓房</div>
            </div>
            <div class="header-stat">
                <div class="header-stat-value">5300</div>
                <div class="header-stat-label">总存量(吨)</div>
            </div>
            <div class="header-stat">
                <div class="header-stat-value">18.5°C</div>
                <div class="header-stat-label">平均温度</div>
            </div>
            <div class="header-stat">
                <div class="header-stat-value status-warning">1</div>
                <div class="header-stat-label">异常仓房</div>
            </div>
        </div>
    </div>
    
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showReport('daily')">📅 日报</button>
        <button class="nav-tab" onclick="showReport('weekly')">📊 周报</button>
        <button class="nav-tab" onclick="showReport('monthly')">📈 月报</button>
    </div>
    
    <div class="container">
        <!-- 日报内容 -->
        <div id="daily" class="report-content active">
            <!-- 各仓房状况 -->
            <div class="card">
                <div class="card-header">
                    🌾 各仓房状况
                </div>
                <div class="card-content">
                    <div class="warehouse-grid">
                        <div class="warehouse-item normal">
                            <div class="warehouse-header">
                                <div class="warehouse-name">1号仓</div>
                                <div class="warehouse-status status-normal">✅ 正常</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">品种:</span>
                                    <span class="detail-value">稻谷</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">存量:</span>
                                    <span class="detail-value">2000吨</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">温度:</span>
                                    <span class="detail-value">18.2°C</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">湿度:</span>
                                    <span class="detail-value">55%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="warehouse-item warning">
                            <div class="warehouse-header">
                                <div class="warehouse-name">3号仓</div>
                                <div class="warehouse-status status-warning">⚠️ 关注</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">品种:</span>
                                    <span class="detail-value">小麦</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">存量:</span>
                                    <span class="detail-value">1500吨</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">温度:</span>
                                    <span class="detail-value">19.1°C <span class="trend-indicator trend-up">↑</span></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">湿度:</span>
                                    <span class="detail-value">62%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="warehouse-item normal">
                            <div class="warehouse-header">
                                <div class="warehouse-name">5号仓</div>
                                <div class="warehouse-status status-normal">✅ 正常</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">品种:</span>
                                    <span class="detail-value">玉米</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">存量:</span>
                                    <span class="detail-value">1800吨</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">温度:</span>
                                    <span class="detail-value">17.8°C</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">湿度:</span>
                                    <span class="detail-value">58%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 今日预警 -->
            <div class="card">
                <div class="card-header">
                    ⚠️ 今日预警
                </div>
                <div class="card-content">
                    <ul class="alert-list">
                        <li class="alert-item alert-warning">
                            <span class="alert-icon">🟡</span>
                            <div>
                                <strong>3号仓温度偏高</strong><br>
                                当前19.1°C，建议立即通风降温
                            </div>
                        </li>
                        <li class="alert-item alert-warning">
                            <span class="alert-icon">🟡</span>
                            <div>
                                <strong>5号仓湿度上升</strong><br>
                                需要密切监控变化趋势
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 今日作业 -->
            <div class="card">
                <div class="card-header">
                    💡 今日作业安排
                </div>
                <div class="card-content">
                    <ul class="schedule-list">
                        <li class="schedule-item">
                            <div class="schedule-time">上午 8:00-12:00</div>
                            <ul class="schedule-tasks">
                                <li>3号仓: 开启通风设备，降温处理</li>
                                <li>1号仓: 常规巡检，重点检查东南角</li>
                                <li>5号仓: 湿度监测，记录变化</li>
                            </ul>
                        </li>
                        <li class="schedule-item">
                            <div class="schedule-time">下午 14:00-18:00</div>
                            <ul class="schedule-tasks">
                                <li>3号仓: 检查通风效果，记录温度变化</li>
                                <li>全部仓房: 安全检查，设备维护</li>
                                <li>数据记录: 更新各仓房监测数据</li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出</button>
                <button class="btn btn-secondary">📧 分享</button>
                <button class="btn btn-success">✅ 确认</button>
            </div>
        </div>
        
        <!-- 周报内容 -->
        <div id="weekly" class="report-content">
            <div class="card">
                <div class="card-header">
                    🏆 本周表现排名
                </div>
                <div class="card-content">
                    <div class="warehouse-grid">
                        <div class="warehouse-item normal">
                            <div class="warehouse-header">
                                <div class="warehouse-name">🥇 1号仓</div>
                                <div class="warehouse-status status-normal">优秀</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">平均温度:</span>
                                    <span class="detail-value">18.2°C</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">异常次数:</span>
                                    <span class="detail-value">0次</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="warehouse-item normal">
                            <div class="warehouse-header">
                                <div class="warehouse-name">🥈 5号仓</div>
                                <div class="warehouse-status status-normal">良好</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">平均温度:</span>
                                    <span class="detail-value">17.8°C</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">异常次数:</span>
                                    <span class="detail-value">0次</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="warehouse-item warning">
                            <div class="warehouse-header">
                                <div class="warehouse-name">⚠️ 3号仓</div>
                                <div class="warehouse-status status-warning">需关注</div>
                            </div>
                            <div class="warehouse-details">
                                <div class="detail-item">
                                    <span class="detail-label">平均温度:</span>
                                    <span class="detail-value">19.1°C</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">异常次数:</span>
                                    <span class="detail-value">2次</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出周报</button>
                <button class="btn btn-secondary">📧 发送邮件</button>
            </div>
        </div>
        
        <!-- 月报内容 -->
        <div id="monthly" class="report-content">
            <div class="card">
                <div class="card-header">
                    📈 月度概况
                </div>
                <div class="card-content">
                    <div class="quick-stats">
                        <div class="quick-stat">
                            <div class="quick-stat-value">18.5°C</div>
                            <div class="quick-stat-label">月均温度</div>
                        </div>
                        <div class="quick-stat">
                            <div class="quick-stat-value">58%</div>
                            <div class="quick-stat-label">月均湿度</div>
                        </div>
                        <div class="quick-stat">
                            <div class="quick-stat-value">6次</div>
                            <div class="quick-stat-label">异常总数</div>
                        </div>
                        <div class="quick-stat">
                            <div class="quick-stat-value">良好</div>
                            <div class="quick-stat-label">月度评级</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-primary">📄 导出月报</button>
                <button class="btn btn-secondary">📧 发送邮件</button>
            </div>
        </div>
    </div>
    
    <!-- 浮动操作按钮 -->
    <button class="floating-action" onclick="quickAction()">
        ⚡
    </button>
    
    <script>
        // 更新当前时间
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('currentTime').textContent = timeString;
        }
        
        // 切换报表
        function showReport(reportType) {
            // 隐藏所有报表内容
            const contents = document.querySelectorAll('.report-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的活动状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的报表内容
            document.getElementById(reportType).classList.add('active');
            
            // 激活对应的标签
            event.target.classList.add('active');
        }
        
        // 快速操作
        function quickAction() {
            const actions = [
                '📞 紧急联系',
                '📝 快速记录',
                '📸 拍照上传',
                '🔔 发送预警'
            ];
            
            const action = prompt('选择快速操作:\n' + actions.join('\n'));
            if (action) {
                alert('执行操作: ' + action);
            }
        }
        
        // 触摸滑动支持
        let startX = 0;
        let currentTab = 0;
        const tabs = ['daily', 'weekly', 'monthly'];
        
        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        document.addEventListener('touchend', function(e) {
            const endX = e.changedTouches[0].clientX;
            const diffX = startX - endX;
            
            if (Math.abs(diffX) > 50) { // 滑动距离阈值
                if (diffX > 0 && currentTab < tabs.length - 1) {
                    // 向左滑动，下一个标签
                    currentTab++;
                } else if (diffX < 0 && currentTab > 0) {
                    // 向右滑动，上一个标签
                    currentTab--;
                }
                
                // 切换到对应标签
                const tabButtons = document.querySelectorAll('.nav-tab');
                tabButtons[currentTab].click();
            }
        });
        
        // 初始化
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新一次时间
        
        // 防止页面缩放
        document.addEventListener('gesturestart', function (e) {
            e.preventDefault();
        });
        
        document.addEventListener('gesturechange', function (e) {
            e.preventDefault();
        });
        
        document.addEventListener('gestureend', function (e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
