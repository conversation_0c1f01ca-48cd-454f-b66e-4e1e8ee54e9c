<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巡查预警页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .top-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            height: 300px;
        }
        
        .overview-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .regional-section {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #1976d2;
            padding-bottom: 8px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .sub-stat {
            margin-left: 20px;
            font-size: 13px;
        }
        
        .regional-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .city-name {
            font-weight: bold;
            color: #333;
        }
        
        .city-stats {
            font-size: 13px;
            color: #666;
        }
        
        .details-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .query-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .query-row {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
            align-items: center;
        }
        
        .query-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .query-label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
        }
        
        .query-input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .query-select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            min-width: 120px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        
        .status-pending {
            color: #ff9800;
            font-weight: bold;
        }
        
        .status-processing {
            color: #2196f3;
            font-weight: bold;
        }
        
        .status-overdue {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-completed {
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部两个区域 -->
        <div class="top-section">
            <!-- 第一块：左上角 - 巡查预警概况 -->
            <div class="overview-section">
                <div class="section-title">巡查预警概况</div>
                <div class="stat-item">
                    <span class="stat-label">待处置预警</span>
                    <span class="stat-value">125条</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">处置中预警</span>
                    <span class="stat-value">89条</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">超期未处置预警</span>
                    <span class="stat-value">23条</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">已处置预警</span>
                    <span class="stat-value">1,256条</span>
                </div>
                <div class="stat-item sub-stat">
                    <span class="stat-label">├─ 判定为风险的数据条数</span>
                    <span class="stat-value">892条</span>
                </div>
                <div class="stat-item sub-stat">
                    <span class="stat-label">└─ 判定为非风险的数据条数</span>
                    <span class="stat-value">364条</span>
                </div>
            </div>
            
            <!-- 第二块：右上方 - 各地待处置风险汇总 -->
            <div class="regional-section">
                <div class="section-title">江西省各地待处置风险汇总</div>
                <div class="regional-item">
                    <span class="city-name">南昌市</span>
                    <span class="city-stats">待处置25 | 处置中18 | 超期5</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">九江市</span>
                    <span class="city-stats">待处置19 | 处置中12 | 超期3</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">景德镇市</span>
                    <span class="city-stats">待处置8 | 处置中6 | 超期1</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">萍乡市</span>
                    <span class="city-stats">待处置12 | 处置中9 | 超期2</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">新余市</span>
                    <span class="city-stats">待处置6 | 处置中4 | 超期1</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">鹰潭市</span>
                    <span class="city-stats">待处置5 | 处置中3 | 超期0</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">赣州市</span>
                    <span class="city-stats">待处置15 | 处置中11 | 超期4</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">宜春市</span>
                    <span class="city-stats">待处置10 | 处置中7 | 超期2</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">上饶市</span>
                    <span class="city-stats">待处置13 | 处置中8 | 超期3</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">吉安市</span>
                    <span class="city-stats">待处置9 | 处置中6 | 超期1</span>
                </div>
                <div class="regional-item">
                    <span class="city-name">抚州市</span>
                    <span class="city-stats">待处置7 | 处置中5 | 超期1</span>
                </div>
            </div>
        </div>
        
        <!-- 第三块：页面下方 - 预警详情数据列表 -->
        <div class="details-section">
            <div class="section-title">预警详情</div>
            
            <!-- 查询条件 -->
            <div class="query-section">
                <div class="query-row">
                    <div class="query-item">
                        <span class="query-label">预警日期：</span>
                        <input type="date" class="query-input">
                        <span>至</span>
                        <input type="date" class="query-input">
                    </div>
                    <div class="query-item">
                        <span class="query-label">库点：</span>
                        <select class="query-select">
                            <option>请选择</option>
                            <option>南昌市粮库</option>
                            <option>九江市粮库</option>
                        </select>
                    </div>
                    <div class="query-item">
                        <span class="query-label">行政隶属：</span>
                        <select class="query-select">
                            <option>请选择</option>
                            <option>省级</option>
                            <option>市级</option>
                            <option>县级</option>
                        </select>
                    </div>
                </div>
                <div class="query-row">
                    <div class="query-item">
                        <span class="query-label">监管类别：</span>
                        <select class="query-select">
                            <option>请选择</option>
                            <option>安全生产</option>
                            <option>质量安全</option>
                            <option>储备管理</option>
                        </select>
                    </div>
                    <div class="query-item">
                        <span class="query-label">问题分类：</span>
                        <select class="query-select">
                            <option>请选择</option>
                            <option>设施设备</option>
                            <option>管理制度</option>
                            <option>操作规范</option>
                        </select>
                    </div>
                    <div class="query-item">
                        <span class="query-label">问题编号：</span>
                        <input type="text" class="query-input" placeholder="请输入问题编号">
                    </div>
                    <div class="query-item">
                        <span class="query-label">处置状态：</span>
                        <select class="query-select">
                            <option>请选择</option>
                            <option>待处置</option>
                            <option>处置中</option>
                            <option>超期未处置</option>
                            <option>已处置</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 数据列表 -->
            <table class="data-table">
                <thead>
                    <tr>
                        <th>预警时间</th>
                        <th>问题发现途径</th>
                        <th>库点</th>
                        <th>行政隶属</th>
                        <th>监管类别</th>
                        <th>问题分类</th>
                        <th>问题编号</th>
                        <th>处置状态</th>
                        <th>处置截止时间</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2024-01-15 09:30</td>
                        <td>日常巡查</td>
                        <td>南昌市第一粮库</td>
                        <td>市级</td>
                        <td>安全生产</td>
                        <td>设施设备</td>
                        <td>JX2024001</td>
                        <td><span class="status-pending">待处置</span></td>
                        <td>2024-01-20</td>
                    </tr>
                    <tr>
                        <td>2024-01-14 14:20</td>
                        <td>监测预警</td>
                        <td>九江市粮食储备库</td>
                        <td>市级</td>
                        <td>质量安全</td>
                        <td>管理制度</td>
                        <td>JX2024002</td>
                        <td><span class="status-processing">处置中</span></td>
                        <td>2024-01-19</td>
                    </tr>
                    <tr>
                        <td>2024-01-13 11:45</td>
                        <td>举报投诉</td>
                        <td>景德镇市粮库</td>
                        <td>县级</td>
                        <td>储备管理</td>
                        <td>操作规范</td>
                        <td>JX2024003</td>
                        <td><span class="status-overdue">超期未处置</span></td>
                        <td>2024-01-18</td>
                    </tr>
                    <tr>
                        <td>2024-01-12 16:10</td>
                        <td>专项检查</td>
                        <td>萍乡市储备粮库</td>
                        <td>市级</td>
                        <td>安全生产</td>
                        <td>设施设备</td>
                        <td>JX2024004</td>
                        <td><span class="status-completed">已处置</span></td>
                        <td>2024-01-17</td>
                    </tr>
                    <tr>
                        <td>2024-01-11 08:30</td>
                        <td>日常巡查</td>
                        <td>新余市粮食库</td>
                        <td>县级</td>
                        <td>质量安全</td>
                        <td>管理制度</td>
                        <td>JX2024005</td>
                        <td><span class="status-completed">已处置</span></td>
                        <td>2024-01-16</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
