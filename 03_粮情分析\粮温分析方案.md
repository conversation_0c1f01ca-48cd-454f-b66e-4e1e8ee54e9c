**核心理念：从"预测"到"诊断"的转变**

预测模型回答"未来会怎样？"，而诊断模型回答"现在发生了什么？为什么？"。这是一个典型的**模式识别与诊断专家系统**问题，要实现这一点，我们不能直接将原始温度序列输入模型，而是需要一个强大的**特征工程模块**，将原始数据转化为模型可以理解的"粮情模式指标"。

**三模块智能诊断系统架构**

1.  **模块一：多维度特征工程引擎**

    - **任务：**
      从原始的时空（时间+空间）粮温数据中，计算一系列能够表征粮堆状态的量化指标。

    - **输入：** 所有测温点的历史温度序列及其三维坐标 (x, y, z)。

    - **输出：** 一个在特定时间点（例如"今天"）的"粮情快照"特征向量。

2.  **模块二：AI诊断模型（多标签分类）**

    - **任务：**
      基于特征工程引擎输出的"粮情快照"，判断当前是否存在一种或多种异常模式。

    - **输入：** 特征向量。

    - **输出：**
      一组诊断标签（例如，"表层局部发热"、"粮温升温异常"等）。

3.  **模块三：专家知识库与建议生成器**

    - **任务：**
      根据AI模型的诊断标签，匹配知识库，生成人类可读的分析报告和操作建议。

    - **输入：** 诊断标签。

    - **输出：** 专家建议。

**模块一：多维度特征工程引擎**

这是整个系统的基石。我们将从**点、线、面、体**四个维度来设计特征，全面刻画粮情。

假设我们在 t 时刻进行分析，回看过去 d 天的数据。

**1. 单点特征 (Point-level Features)**

*针对每个测温点 i 计算*

| 特征名称               | 计算方法                   | 诊断意义                                     |
|------------------------|----------------------------|----------------------------------------------|
| T_current             | T_i(t)                     | 当前绝对温度                                 |
| Rate_of_Change_24h    | T_i(t) - T_i(t-24h)        | 24小时温升，检测快速发热                     |
| Rate_of_Change_7d     | T_i(t) - T_i(t-7d)         | 7日温升，检测缓慢、持续发热                  |
| Neighbor_Deviation    | T_i(t) - mean(T_neighbors(t)) | 与空间相邻点的温差，检测**局部异常点**         |
| Layer_Deviation       | T_i(t) - mean(T_layer(t))  | 与所在层/圈平均温的温差，检测**同层/圈异常**  |
| Volatility_7d         | std(T_i(t-7d:t))           | 过去7天温度的标准差，检测**测温电缆/点位异常**（正常粮温波动小） |

**2. 聚合特征 (Aggregate-level Features)**

*按区域（层、圈、垂直区域、全仓）进行聚合计算*

| 特征名称                  | 计算方法                     | 诊断意义                       |
|---------------------------|------------------------------|--------------------------------|
| Region_T_Max             | max(T_region(t))             | 区域最高温度，检测**超温**     |
| Region_T_Mean            | mean(T_region(t))            | 区域平均温度，评估**整体热状况** |
| Region_T_Std             | std(T_region(t))             | 区域内温度标准差，**核心指标！检测发热的局部性** |
| Region_Rate_Mean         | mean(Rate_of_Change_7d)      | 区域平均温升速率               |
| Region_High_Temp_Ratio   | count(T_i > 阈值) / count(T_i) | 区域内高温点占比               |

**区域定义 (Region Definition):**

- **表层 (Surface):** Z坐标 > 粮堆高度的80%
- **中部 (Middle):** 20% < Z坐标 < 80%
- **底部 (Bottom):** Z坐标 < 20%
- **外圈 (Outer Ring):** 径向坐标 > 仓储半径的70% (仅立筒仓)
- **垂直区域 (Vertical Column):** 某个(X, Y)坐标附近的所有测温点集合

**3. 空间梯度特征 (Spatial-Gradient Features)**

*计算不同区域间的温差关系*

| 特征名称              | 计算方法                       | 诊断意义                             |
|-----------------------|--------------------------------|--------------------------------------|
| Vertical_Gradient    | Mean(T_surface) - Mean(T_bottom) | 垂直温差，判断热量积聚在顶部还是底部 |
| Radial_Gradient      | Mean(T_outer_ring) - Mean(T_inner_ring) | 径向温差，判断热量在中心还是外围     |
| Max_Layer_Diff       | max(Mean(T_layer)) - min(Mean(T_layer)) | 最大层间温差，评估垂直方向的温度均匀性 |

**模块二：AI诊断模型（多标签分类）**

有了上述特征向量后，我们可以构建一个AI模型来自动进行诊断。

**1. 方法选择：监督学习 - 多标签分类**

- **输入 (X):** 在 t 时刻，将模块一计算出的所有特征（例如，100多个特征）组合成一个长向量。
- **输出 (Y):** 一个多维度的二进制向量，每一位对应一种异常诊断标签。例如：

\[ [0, 1, 0, 1, 0, \ldots] \]

这表示第2种（如同层异常）和第4种（如表层局部发热）异常被激活。

- **模型选型：**
  - **梯度提升树 (如 LightGBM, XGBoost):** **强烈推荐**。对于这种结构化的表格特征数据，效果最好，速度快，且能提供特征重要性分析，具有很好的可解释性。
  - **深度神经网络 (MLP):** 也可以使用，但对于这类问题，通常不比树模型有优势，且更像一个"黑盒"。

**2. 数据准备：需要历史标注数据**

这是监督学习的关键。您需要邀请粮食仓储专家，对历史数据进行回顾性标注。专家根据历史粮温图和操作记录，为每个时间点打上当时存在的异常标签。这个标注数据集是训练高质量AI诊断模型的宝贵资产。

**3. 诊断标签体系 (Y - a.k.a. Analysis Results)**

基于您的需求和行业知识，我设计的诊断标签如下：

| 标签ID | 诊断标签             | 关键判断特征 (示例)                                  |
|--------|----------------------|------------------------------------------------------|
| 1      | **硬件异常-点位**   | Volatility_7d, Neighbor_Deviation 极高               |
| 2      | **硬件异常-线缆**   | 同一电缆上多个点位的 Volatility_7d 异常             |
| 3      | **同层/圈不均**     | 某个 Layer/Ring 的 Region_T_Std 很高                |
| 4      | **温升速率异常**    | 某个点/区域的 Rate_of_Change 超过阈值               |
| 5      | **超最高温**        | Region_T_Max 超过安全阈值 (如 30°C)                 |
| 6      | **表层-局部发热**   | Surface区域 Region_T_Std 很高，且 Region_T_Max 较高 |
| 7      | **表层-整体发热**   | Surface区域 Region_T_Std 很低，但 Region_T_Mean 很高/持续上升 |
| 8      | **中部-局部发热**   | Middle区域 Region_T_Std 很高，且 Region_T_Max 较高  |
| 9      | **中部-整体发热**   | Middle区域 Region_T_Std 很低，但 Region_T_Mean 很高/持续上升 |
| 10     | **底部-局部发热**   | Bottom区域 Region_T_Std 很高，且 Region_T_Max 较高  |
| 11     | **底部-整体发热**   | Bottom区域 Region_T_Std 很低，但 Region_T_Mean 很高/持续上升 |
| 12     | **垂直-局部发热**   | 某个Vertical Column的Region_T_Mean远高于其他列      |
| 13     | **垂直-整体发热**   | 多个Vertical Column的Region_T_Mean整体上升          |
| 14     | **整仓发热**        | Whole Silo 的 Region_T_Mean 持续上升                |
| 15     | **外圈粮温过高**    | Radial_Gradient 为显著正值                          |
| 16     | **仓内温差过大**    | Max_Layer_Diff 或 Radial_Gradient 绝对值过大        |
| 17     | **新粮入仓后熟**    | (需结合入仓时间) 初期整体小幅温升                   |
| 18     | **状态稳定**        | 所有指标均在正常范围内                              |

**模块三：专家知识库与建议生成器**

这个模块将AI模型的机器语言（诊断标签）翻译成人类的行动指南。这是一个基于规则的系统，将诊断结果映射到原因分析和处理建议。

| 诊断结果 (AI Output)      | 可能原因分析                                                                    | 专家建议 (Actionable Advice)                                                                                                                                                             |
|---------------------------|---------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **硬件异常-点位/线缆**    | 传感器损坏、信号线接触不良或老化。                                              | 1. **记录**：标记该点位/线缆数据为"不可靠"。<br>2. **核查**：在后续空仓期进行检修或更换。<br>3. **关注**：加强对该异常点周围区域的人工巡检。                                               |
| **表层-局部发热**         | 仓顶漏水、表层结露导致局部水分升高；害虫活动初期。                              | 1. **首要核查**：检查仓顶密封性，确认有无渗漏。<br>2. **感官检查**：进仓检查粮面有无异味、霉变、结块或虫迹。<br>3. **处理**：若情况轻微，可启动仓顶风机进行水平通风；若严重，需进行局部扒粮处理。 |
| **表层-整体发热**         | 白天外界高温经仓顶和仓壁传入；上层空间通风不良。                                | 1. **优化操作**：在夜间或外界气温较低时段，开启轴流风机进行仓内上层空间通风换气。<br>2. **隔热**：检查仓顶隔热层是否完好，考虑进行屋顶喷白或加装遮阳设施。                                 |
| **中部/底部-局部发热**   | **高度危险信号！** 内部水分转移、杂质聚集或高水分粮团引起的"湿热"发热，可能是害虫或霉菌的温床。 | 1. **立即行动**：对发热点周围进行加密测温，确认发热核心和范围。<br>2. **通风决策**：若温差和湿度允许，立即启动机械通风，对发热区域进行强力降温。<br>3. **预案准备**：若通风无法抑制，立即准备倒仓或出仓方案，决不能拖延。 |
| **中部/底部-整体发热**    | 粮食品质劣变，呼吸作用增强；储藏时间过长，粮堆自然熟化。                        | 1. **评估粮情**：取样化验粮食水分、脂肪酸值等指标，评估粮食品质。<br>2. **强化通风**：制定并执行周期性降温通风计划，整体降低粮堆温度。<br>3. **轮换计划**：将此仓粮食的轮换优先级提前。       |
| **外圈粮温过高**          | 仓壁保温性能差，白天外界热量传入；或立筒仓壁附近杂质较多。                      | 1. **日常管理**：加强对仓壁的隔热保护，如外墙刷白漆。<br>2. **通风策略**：在设计通风方案时，确保外圈区域有足够风量通过。<br>3. **入仓管理**：在入粮时，使用清扫器减少杂质在仓壁附近的积存。 |
| **整仓发热**              | 粮食整体水分偏高；通风系统失效或未及时使用；大范围的生物活动。                  | 1. **全面检查**：立即检查通风系统（风机、风道）是否工作正常。<br>2. **紧急通风**：在气象条件允许时，进行最大功率的降温或降水通风。<br>3. **重新评估**：全面化验粮情，重新评估此仓粮食的安全储存期。 |

**整体工作流程**

1.  **数据采集：** 系统每日定时（如每隔4小时）采集所有测温点数据。
2.  **特征计算：** 模块一启动，基于最新的数据和过去7天的历史，计算出当前时刻的"粮情快照"特征向量。
3.  **AI诊断：** 模块二接收特征向量，输出当前的异常诊断标签集合。
4.  **建议生成：** 如果诊断结果中包含"状态稳定"之外的任何标签，模块三将启动，匹配知识库，生成详细的分析报告和操作建议。
5.  **告警与展示：** 系统通过看板、短信或App推送，向保管员发出告警，内容包括：**"A号仓发现'中部局部发热'，AI分析原因为...，建议立即..."**，并附上粮温变化图和发热区域的可视化。

这套从特征工程到AI诊断，再到专家建议的完整方案，能够将您的粮温数据转化为真正具有洞察力和决策支持能力的智能仓储大脑。